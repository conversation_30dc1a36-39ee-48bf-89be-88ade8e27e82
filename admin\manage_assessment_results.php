<?php
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';

$message = "";
$assessment_id = isset($_GET['assessment_id']) ? (int)$_GET['assessment_id'] : 0;

// Fetch assessment details
$assessment = null;
if ($assessment_id > 0) {
    $stmt = $conn->prepare("
        SELECT a.*, s.subject_name, c.class_name
        FROM assessment a
        JOIN subjects s ON a.subject_id = s.subject_id
        JOIN classrooms c ON a.classroom_id = c.classroom_id
        WHERE a.assessment_id = ?
    ");
    $stmt->bind_param("i", $assessment_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 1) {
        $assessment = $result->fetch_assoc();
    } else {
        $message = "❌ Penilaian tidak dijumpai.";
    }
    $stmt->close();
}

// Handle marks submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['marks']) && $assessment) {
    $marks_data = $_POST['marks'];
    $success_count = 0;
    $error_count = 0;

    foreach ($marks_data as $student_id => $marks) {
        $student_id = (int)$student_id;
        $marks = (float)$marks;

        if ($marks >= 0) { // Allow 0 marks
            // Check if result already exists
            $check_stmt = $conn->prepare("SELECT result_id FROM assessment_result WHERE assessment_id = ? AND student_id = ?");
            $check_stmt->bind_param("ii", $assessment_id, $student_id);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();

            if ($check_result->num_rows > 0) {
                // Update existing result
                $update_stmt = $conn->prepare("UPDATE assessment_result SET marks = ? WHERE assessment_id = ? AND student_id = ?");
                $update_stmt->bind_param("dii", $marks, $assessment_id, $student_id);
                if ($update_stmt->execute()) {
                    $success_count++;
                } else {
                    $error_count++;
                }
                $update_stmt->close();
            } else {
                // Insert new result
                $insert_stmt = $conn->prepare("INSERT INTO assessment_result (assessment_id, student_id, marks) VALUES (?, ?, ?)");
                $insert_stmt->bind_param("iid", $assessment_id, $student_id, $marks);
                if ($insert_stmt->execute()) {
                    $success_count++;
                } else {
                    $error_count++;
                }
                $insert_stmt->close();
            }
            $check_stmt->close();
        }
    }

    if ($success_count > 0) {
        $message = "✅ $success_count markah berjaya disimpan.";
        if ($error_count > 0) {
            $message .= " $error_count markah gagal disimpan.";
        }
    } else {
        $message = "❌ Tiada markah yang disimpan.";
    }
}

// Fetch students in the assessment's classroom with their current marks
$students = [];
if ($assessment) {
    $students_stmt = $conn->prepare("
        SELECT s.student_id, s.full_name, s.no_ic,
               ar.marks
        FROM students s
        LEFT JOIN assessment_result ar ON s.student_id = ar.student_id AND ar.assessment_id = ?
        WHERE s.classroom_id = ?
        ORDER BY s.full_name
    ");
    $students_stmt->bind_param("ii", $assessment_id, $assessment['classroom_id']);
    $students_stmt->execute();
    $result = $students_stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $students[] = $row;
    }
    $students_stmt->close();
}
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Urus Markah Penilaian - SMKTMI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>

<style>
/* Sidebar Styling */
.sidebar {
    position: fixed;
    top: 0; left: 0;
    width: 300px;
    height: 100vh;
    background-color: #2c3e50;
    padding: 30px 20px;
    color: #ecf0f1;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow-y: auto;
    box-shadow: 5px 0 15px rgba(0,0,0,0.4);
    border-right: 1px solid #34495e;
}

.sidebar h4 {
    font-size: 18px;
    font-weight: 700;
    text-transform: uppercase;
    margin: 30px 0 12px;
    border-bottom: 2px solid #3498db;
    padding-bottom: 6px;
    letter-spacing: 1.5px;
}

.sidebar a {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    color: #bdc3c7;
    text-decoration: none;
    font-size: 16px;
    margin-bottom: 8px;
    border-radius: 6px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.sidebar a i {
    margin-right: 12px;
    width: 18px;
    text-align: center;
}

.sidebar a:hover {
    background-color: #2980b9;
    color: #ecf0f1;
}

/* Content Styling */
.content {
    margin-left: 280px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.form-container {
    width: 100%;
    max-width: 1200px;
    background-color: #fff;
    padding: 30px 40px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0 auto;
}

.form-container h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 25px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    color: #2980b9;
    text-align: center;
}

.alert {
    padding: 15px;
    background-color: #d9edf7;
    border: 1px solid #bce8f1;
    border-radius: 5px;
    color: #31708f;
    margin-bottom: 25px;
    text-align: center;
}

.alert-success {
    background-color: #dff0d8;
    border-color: #d6e9c6;
    color: #3c763d;
}

.alert-danger {
    background-color: #f2dede;
    border-color: #ebccd1;
    color: #a94442;
}

.assessment-info {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
    border-left: 4px solid #3498db;
}

.assessment-info h4 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.info-item {
    display: flex;
    justify-content: space-between;
}

.info-label {
    font-weight: 600;
    color: #34495e;
}

.info-value {
    color: #2c3e50;
}

.marks-table {
    margin-top: 20px;
}

.marks-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

.marks-input {
    width: 80px;
    padding: 5px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
}

.marks-input:focus {
    border-color: #3498db;
    outline: none;
}

.btn-primary {
    background-color: #3498db;
    border-color: #3498db;
    padding: 10px 30px;
    font-weight: 600;
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

.btn-secondary {
    background-color: #95a5a6;
    border-color: #95a5a6;
    padding: 10px 20px;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
    border-color: #7f8c8d;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
        box-shadow: none;
    }
    .content {
        margin-left: 0;
        padding: 20px;
    }
    .info-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<div class="sidebar">
    <h4>Menu Admin</h4>
    <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>

    <h4>Pengurusan Guru</h4>
    <a href="register_teacher.php"><i class="fas fa-user-plus"></i> Daftar Guru</a>
    <a href="list_teachers.php"><i class="fas fa-user-edit"></i> Kemaskini Maklumat Guru</a>

    <h4>Pengurusan Kelas</h4>
    <a href="add_classroom.php"><i class="fas fa-chalkboard"></i> Daftar Kelas</a>
    <a href="assign_teacher.php"><i class="fas fa-user-tie"></i> Tetapan Guru Kelas</a>

    <h4>Pengurusan Subjek</h4>
    <a href="add_subject.php"><i class="fas fa-book"></i> Daftar Subjek</a>
    <a href="assign_teacher_subject_classroom.php"><i class="fas fa-tasks"></i> Tetapan Guru-Subjek-Kelas</a>

    <h4>Pengurusan Penilaian</h4>
    <a href="manage_assessment.php"><i class="fas fa-clipboard-list"></i> Urus Penilaian</a>

    <h4>Pengurusan Jadual</h4>
    <a href="manage_schedules.php"><i class="fas fa-calendar-alt"></i> Urus Jadual Waktu</a>

    <h4>Pengurusan Disiplin</h4>
    <a href="manage_discipline.php"><i class="fas fa-exclamation-triangle"></i> Urus Rekod Disiplin</a>

    <h4>Log Sistem</h4>
    <a href="view_admin_logs.php"><i class="fas fa-history"></i> Log Aktiviti Sistem</a>

    <h4>Sistem</h4>
    <a href="change_password.php"><i class="fas fa-shield-alt"></i> Tukar Kata Laluan</a>
    <a href="logout.php"><i class="fas fa-sign-out-alt"></i> Log Keluar</a>
</div>

    <h4>Pengurusan Penilaian</h4>
    <a href="manage_assessment.php"><i class="fas fa-clipboard-list"></i> Urus Penilaian</a>

    <h4>Sistem</h4>
    <a href="change_password.php"><i class="fas fa-shield-alt"></i> Tukar Kata Laluan</a>
    <a href="logout.php"><i class="fas fa-sign-out-alt"></i> Log Keluar</a>
</div>

<div class="content">
  <div class="form-container">
    <h2>Urus Markah Penilaian</h2>

    <?php if (!empty($message)): ?>
      <div class="alert <?php echo strpos($message, '✅') !== false ? 'alert-success' : 'alert-danger'; ?>">
        <?php echo htmlspecialchars($message); ?>
      </div>
    <?php endif; ?>

    <?php if ($assessment): ?>
      <!-- Assessment Information -->
      <div class="assessment-info">
        <h4><i class="fas fa-clipboard-list"></i> Maklumat Penilaian</h4>
        <div class="info-grid">
          <div class="info-item">
            <span class="info-label">Jenis Penilaian:</span>
            <span class="info-value"><?php echo htmlspecialchars($assessment['assessment_type']); ?></span>
          </div>
          <div class="info-item">
            <span class="info-label">Tarikh:</span>
            <span class="info-value"><?php echo date('d/m/Y', strtotime($assessment['assessment_date'])); ?></span>
          </div>
          <div class="info-item">
            <span class="info-label">Subjek:</span>
            <span class="info-value"><?php echo htmlspecialchars($assessment['subject_name']); ?></span>
          </div>
          <div class="info-item">
            <span class="info-label">Kelas:</span>
            <span class="info-value"><?php echo htmlspecialchars($assessment['class_name']); ?></span>
          </div>
        </div>
      </div>

      <?php if (count($students) > 0): ?>
        <!-- Marks Entry Form -->
        <form method="post">
          <table class="table table-bordered marks-table">
            <thead class="table-light">
              <tr>
                <th>Bil.</th>
                <th>Nama Pelajar</th>
                <th>No. Kad Pengenalan</th>
                <th>Markah</th>
              </tr>
            </thead>
            <tbody>
              <?php foreach ($students as $index => $student): ?>
                <tr>
                  <td><?php echo $index + 1; ?></td>
                  <td><?php echo htmlspecialchars($student['full_name']); ?></td>
                  <td><?php echo htmlspecialchars($student['no_ic']); ?></td>
                  <td>
                    <input type="number"
                           name="marks[<?php echo $student['student_id']; ?>]"
                           class="marks-input"
                           min="0"
                           max="100"
                           step="0.1"
                           value="<?php echo $student['marks'] !== null ? number_format($student['marks'], 1) : ''; ?>"
                           placeholder="0.0">
                  </td>
                </tr>
              <?php endforeach; ?>
            </tbody>
          </table>

          <div style="text-align: center; margin-top: 30px;">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-save"></i> Simpan Markah
            </button>
            <a href="manage_assessment.php" class="btn btn-secondary" style="margin-left: 15px;">
              <i class="fas fa-arrow-left"></i> Kembali
            </a>
          </div>
        </form>
      <?php else: ?>
        <div style="text-align: center; padding: 40px;">
          <i class="fas fa-users" style="font-size: 48px; color: #bdc3c7; margin-bottom: 20px;"></i>
          <h4 style="color: #7f8c8d;">Tiada Pelajar Dijumpai</h4>
          <p style="color: #95a5a6;">Tiada pelajar dalam kelas ini untuk penilaian tersebut.</p>
          <a href="manage_assessment.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
          </a>
        </div>
      <?php endif; ?>

    <?php else: ?>
      <div style="text-align: center; padding: 40px;">
        <i class="fas fa-exclamation-triangle" style="font-size: 48px; color: #f39c12; margin-bottom: 20px;"></i>
        <h4 style="color: #e67e22;">Penilaian Tidak Dijumpai</h4>
        <p style="color: #95a5a6;">Sila pilih penilaian yang sah untuk mengurus markah.</p>
        <a href="manage_assessment.php" class="btn btn-primary">
          <i class="fas fa-clipboard-list"></i> Pilih Penilaian
        </a>
      </div>
    <?php endif; ?>
  </div>
</div>

<script>
// Auto-save functionality (optional)
document.addEventListener('DOMContentLoaded', function() {
    const marksInputs = document.querySelectorAll('.marks-input');

    marksInputs.forEach(input => {
        input.addEventListener('input', function() {
            // Validate input
            let value = parseFloat(this.value);
            if (value < 0) this.value = 0;
            if (value > 100) this.value = 100;
        });
    });
});
</script>

</body>
</html>
