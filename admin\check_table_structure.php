<?php
session_start();

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';

echo "<h2>Database Table Structure Check</h2>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    table { border-collapse: collapse; width: 100%; margin: 20px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .error { color: red; }
    .success { color: green; }
    .warning { color: orange; }
</style>";

// Check if teacher_subject_classrooms table exists
echo "<h3>1. Check if teacher_subject_classrooms table exists</h3>";
$table_check = $conn->query("SHOW TABLES LIKE 'teacher_subject_classrooms'");
if ($table_check && $table_check->num_rows > 0) {
    echo "<p class='success'>✅ Table 'teacher_subject_classrooms' exists</p>";
    
    // Show table structure
    echo "<h3>2. Table Structure</h3>";
    $structure = $conn->query("DESCRIBE teacher_subject_classrooms");
    if ($structure) {
        echo "<table>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        while ($row = $structure->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['Field']}</td>";
            echo "<td>{$row['Type']}</td>";
            echo "<td>{$row['Null']}</td>";
            echo "<td>{$row['Key']}</td>";
            echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
            echo "<td>{$row['Extra']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Show sample data
    echo "<h3>3. Sample Data (first 5 rows)</h3>";
    $sample = $conn->query("SELECT * FROM teacher_subject_classrooms LIMIT 5");
    if ($sample && $sample->num_rows > 0) {
        echo "<table>";
        $first_row = true;
        while ($row = $sample->fetch_assoc()) {
            if ($first_row) {
                echo "<tr>";
                foreach (array_keys($row) as $column) {
                    echo "<th>$column</th>";
                }
                echo "</tr>";
                $first_row = false;
            }
            echo "<tr>";
            foreach ($row as $value) {
                echo "<td>" . ($value ?? 'NULL') . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='warning'>⚠️ No data found in table</p>";
    }
    
} else {
    echo "<p class='error'>❌ Table 'teacher_subject_classrooms' does not exist</p>";
    
    // Check for similar table names
    echo "<h3>Available Tables</h3>";
    $all_tables = $conn->query("SHOW TABLES");
    if ($all_tables) {
        echo "<ul>";
        while ($table = $all_tables->fetch_array()) {
            echo "<li>{$table[0]}</li>";
        }
        echo "</ul>";
    }
}

// Check if we need to create the table
if ($table_check->num_rows == 0) {
    echo "<h3>4. Create Missing Table</h3>";
    echo "<p>The table doesn't exist. Here's the SQL to create it:</p>";
    echo "<pre>";
    echo "CREATE TABLE teacher_subject_classrooms (
    tsc_id INT AUTO_INCREMENT PRIMARY KEY,
    teacher_id INT NOT NULL,
    subject_id INT NOT NULL,
    classroom_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (teacher_id) REFERENCES teachers(teacher_id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(subject_id) ON DELETE CASCADE,
    FOREIGN KEY (classroom_id) REFERENCES classrooms(classroom_id) ON DELETE CASCADE,
    UNIQUE KEY unique_assignment (teacher_id, subject_id, classroom_id)
);";
    echo "</pre>";
    
    if (isset($_GET['create']) && $_GET['create'] == '1') {
        $create_sql = "CREATE TABLE teacher_subject_classrooms (
            tsc_id INT AUTO_INCREMENT PRIMARY KEY,
            teacher_id INT NOT NULL,
            subject_id INT NOT NULL,
            classroom_id INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (teacher_id) REFERENCES teachers(teacher_id) ON DELETE CASCADE,
            FOREIGN KEY (subject_id) REFERENCES subjects(subject_id) ON DELETE CASCADE,
            FOREIGN KEY (classroom_id) REFERENCES classrooms(classroom_id) ON DELETE CASCADE,
            UNIQUE KEY unique_assignment (teacher_id, subject_id, classroom_id)
        )";
        
        if ($conn->query($create_sql)) {
            echo "<p class='success'>✅ Table created successfully!</p>";
        } else {
            echo "<p class='error'>❌ Error creating table: " . $conn->error . "</p>";
        }
    } else {
        echo "<p><a href='?create=1' style='background: #007bff; color: white; padding: 10px; text-decoration: none; border-radius: 4px;'>Create Table Now</a></p>";
    }
}
?>
