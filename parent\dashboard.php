<?php
session_start();

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Parent') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';
require '../includes/universal_logger.php';

$userId = $_SESSION['user_id'];

// Fetch parent details + email
$stmt = $conn->prepare("SELECT p.parent_id, p.full_name, p.phone_number, p.address, u.email
                        FROM parents p
                        JOIN users u ON p.user_id = u.user_id
                        WHERE p.user_id = ?");
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();
$parent = $result->fetch_assoc();

if (!$parent) {
    // Debug information
    echo "Debug: User ID = " . $userId . "<br>";
    echo "Debug: Query executed successfully<br>";
    echo "Debug: No parent record found for this user ID<br>";
    die("Maklumat ibu bapa tidak dijumpai. Sila hubungi pentadbir sistem.");
}

$parentId = $parent['parent_id'] ?? null;

// Debug: Show parent data
echo "<!-- Debug: Parent data found -->";
echo "<!-- Parent ID: " . $parentId . " -->";
echo "<!-- Parent Name: " . htmlspecialchars($parent['full_name'] ?? 'N/A') . " -->";

// Update section
$updateMsg = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $updatedName = trim($_POST['full_name']);
    $updatedPhone = trim($_POST['phone_number']);
    $updatedAddress = trim($_POST['address']);

    $updateStmt = $conn->prepare("UPDATE parents SET full_name = ?, phone_number = ?, address = ? WHERE parent_id = ?");
    $updateStmt->bind_param("sssi", $updatedName, $updatedPhone, $updatedAddress, $parentId);
    if ($updateStmt->execute()) {
        // Log the parent profile update with details
        $details = [
            'Nama Lama' => $parent['full_name'],
            'Nama Baru' => $updatedName,
            'Telefon Lama' => $parent['phone_number'],
            'Telefon Baru' => $updatedPhone,
            'Alamat Lama' => $parent['address'],
            'Alamat Baru' => $updatedAddress
        ];
        logUserActivity($conn, 'UPDATE', 'PROFILE', $parentId, $updatedName,
            "Maklumat ibu bapa dikemaskini", $details);

        $updateMsg = "Maklumat berjaya dikemaskini.";
        $parent['full_name'] = $updatedName;
        $parent['phone_number'] = $updatedPhone;
        $parent['address'] = $updatedAddress;
        $_SESSION['name'] = $updatedName;
    } else {
        $updateMsg = "Gagal kemaskini maklumat.";
    }
}

// Get children with PIBG status and academic info
$students = [];
if ($parentId) {
    $studentStmt = $conn->prepare("
        SELECT s.student_id, s.full_name AS student_name, c.class_name, s.pibg_paid, c.classroom_id,
               t.full_name as teacher_name, t.phone_number as teacher_phone
        FROM students s
        LEFT JOIN classrooms c ON s.classroom_id = c.classroom_id
        LEFT JOIN teachers t ON c.teacher_id = t.teacher_id
        WHERE s.parent_id = ?
    ");
    $studentStmt->bind_param("i", $parentId);
    $studentStmt->execute();
    $studentResult = $studentStmt->get_result();
    while ($row = $studentResult->fetch_assoc()) {


        // Get attendance stats for current month
        $currentMonth = date('Y-m');
        $attendanceStmt = $conn->prepare("
            SELECT status, COUNT(*) as count
            FROM attendance
            WHERE student_id = ? AND DATE_FORMAT(attendance_date, '%Y-%m') = ?
            GROUP BY status
        ");
        $attendanceStmt->bind_param("is", $row['student_id'], $currentMonth);
        $attendanceStmt->execute();
        $attendanceResult = $attendanceStmt->get_result();

        $attendanceStats = ['hadir' => 0, 'tidak_hadir' => 0];
        while ($attRow = $attendanceResult->fetch_assoc()) {
            if ($attRow['status'] === 'Hadir') {
                $attendanceStats['hadir'] = $attRow['count'];
            } elseif ($attRow['status'] === 'Tidak Hadir') {
                $attendanceStats['tidak_hadir'] = $attRow['count'];
            }
        }
        $total = $attendanceStats['hadir'] + $attendanceStats['tidak_hadir'];
        $percentage = $total > 0 ? round(($attendanceStats['hadir'] / $total) * 100, 1) : 0;

        $row['attendance_stats'] = $attendanceStats;
        $row['attendance_percentage'] = $percentage;
        $row['attendance_total'] = $total;

        $students[] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Ibu Bapa - SMKTMI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>

<style>
/* Content Styling */
.content {
    margin-left: 280px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.form-container {
    width: 100%;
    max-width: 1200px;
    background-color: #fff;
    padding: 30px 40px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 1.1rem;
    margin: 0 auto;
}

.form-container h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 25px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    color: #2980b9;
    text-align: center;
}

.alert {
    padding: 15px;
    background-color: #d9edf7;
    border: 1px solid #bce8f1;
    border-radius: 5px;
    color: #31708f;
    margin-bottom: 25px;
    text-align: center;
}

.alert-success {
    background-color: #dff0d8;
    border-color: #d6e9c6;
    color: #3c763d;
}

.stats-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-left: 4px solid #3498db;
    transition: transform 0.2s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-primary {
    background-color: #3498db;
    border-color: #3498db;
    padding: 10px 30px;
    font-weight: 600;
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

.student-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    transition: transform 0.2s ease;
}

.student-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.student-card .d-flex {
    font-size: 1.1rem;
}

.student-card i {
    width: 18px;
    text-align: center;
    font-size: 1.1rem;
}

.student-card strong {
    font-size: 1.2rem;
}

.pibg-status {
    font-size: 12px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.pibg-paid {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.pibg-unpaid {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Responsive */
@media (max-width: 768px) {
    .content {
        margin-left: 0;
        padding: 20px;
    }
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
  <div class="form-container">
    <h2><i class="fas fa-home"></i> Dashboard Ibu Bapa</h2>

    <?php if (!empty($updateMsg)): ?>
      <div class="alert alert-success">
        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($updateMsg); ?>
      </div>
    <?php endif; ?>

    <!-- PIBG Summary -->
    <?php if (!empty($students)): ?>
      <?php
      $totalChildren = count($students);
      $paidChildren = array_filter($students, function($s) { return $s['pibg_paid']; });
      $unpaidChildren = $totalChildren - count($paidChildren);
      ?>
      <div class="row mb-4">
        <div class="col-12">
          <div class="stats-card" style="border-left-color: #f39c12;">
            <h5 style="margin-bottom: 20px; color: #2c3e50; text-align: center;">
              <i class="fas fa-chart-pie"></i> Ringkasan Status PIBG
            </h5>
            <div class="row text-center">
              <div class="col-md-4">
                <div class="p-3">
                  <i class="fas fa-users" style="font-size: 24px; color: #3498db; margin-bottom: 10px;"></i>
                  <h4 style="color: #2c3e50; margin-bottom: 5px;"><?php echo $totalChildren; ?></h4>
                  <p style="color: #7f8c8d; margin: 0;">Jumlah Anak</p>
                </div>
              </div>
              <div class="col-md-4">
                <div class="p-3">
                  <i class="fas fa-check-circle" style="font-size: 24px; color: #27ae60; margin-bottom: 10px;"></i>
                  <h4 style="color: #27ae60; margin-bottom: 5px;"><?php echo count($paidChildren); ?></h4>
                  <p style="color: #7f8c8d; margin: 0;">Sudah Bayar PIBG</p>
                </div>
              </div>
              <div class="col-md-4">
                <div class="p-3">
                  <i class="fas fa-exclamation-circle" style="font-size: 24px; color: #e74c3c; margin-bottom: 10px;"></i>
                  <h4 style="color: #e74c3c; margin-bottom: 5px;"><?php echo $unpaidChildren; ?></h4>
                  <p style="color: #7f8c8d; margin: 0;">Belum Bayar PIBG</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    <?php endif; ?>

    <div class="row">
      <!-- Parent Info -->
      <div class="col-md-6">
        <div class="stats-card">
          <h5 style="margin-bottom: 20px; color: #2c3e50; text-align: center;">
            <i class="fas fa-user"></i> Maklumat Peribadi
          </h5>

          <form method="post">
            <div class="mb-3">
              <label class="form-label"><strong>Nama Penuh:</strong></label>
              <input type="text" name="full_name" class="form-control"
                     value="<?php echo htmlspecialchars($parent['full_name'] ?? ''); ?>" required>
            </div>

            <div class="mb-3">
              <label class="form-label"><strong>Alamat Emel:</strong></label>
              <input type="email" class="form-control"
                     value="<?php echo htmlspecialchars($parent['email'] ?? ''); ?>" readonly>
            </div>

            <div class="mb-3">
              <label class="form-label"><strong>Nombor Telefon:</strong></label>
              <input type="text" name="phone_number" class="form-control"
                     value="<?php echo htmlspecialchars($parent['phone_number'] ?? ''); ?>">
            </div>

            <div class="mb-3">
              <label class="form-label"><strong>Alamat:</strong></label>
              <textarea name="address" class="form-control" rows="3"><?php echo htmlspecialchars($parent['address'] ?? ''); ?></textarea>
            </div>

            <div class="text-center">
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> Kemaskini Maklumat
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Children Info -->
      <div class="col-md-6">
        <div class="stats-card">
          <h5 style="margin-bottom: 20px; color: #2c3e50; text-align: center;">
            <i class="fas fa-child"></i> Maklumat Anak
          </h5>

          <?php if (!empty($students)): ?>
            <?php foreach ($students as $s): ?>
              <div class="student-card">
                <div class="d-flex align-items-center mb-2">
                  <i class="fas fa-user-graduate me-2" style="color: #3498db;"></i>
                  <strong><?php echo htmlspecialchars($s['student_name']); ?></strong>
                </div>
                <div class="d-flex align-items-center mb-2">
                  <i class="fas fa-users me-2" style="color: #27ae60;"></i>
                  <span>Kelas: <?php echo htmlspecialchars($s['class_name'] ?? 'Belum Ditetapkan'); ?></span>
                </div>
                <div class="d-flex align-items-center mb-2">
                  <i class="fas fa-calendar-check me-2" style="color: #f39c12;"></i>
                  <span>Kehadiran (<?php echo date('M'); ?>): <?php echo $s['attendance_percentage']; ?>%
                    (<?php echo $s['attendance_stats']['hadir']; ?>/<?php echo $s['attendance_total']; ?> hari)</span>
                </div>
                <?php if ($s['teacher_name']): ?>
                <div class="d-flex align-items-center mb-2">
                  <i class="fas fa-chalkboard-teacher me-2" style="color: #e67e22;"></i>
                  <span>Guru Kelas: <?php echo htmlspecialchars($s['teacher_name']); ?></span>
                </div>
                <div class="d-flex align-items-center mb-2">
                  <i class="fas fa-phone me-2" style="color: #34495e;"></i>
                  <span>Tel. Guru Kelas: <?php echo htmlspecialchars($s['teacher_phone'] ?? '-'); ?></span>
                </div>
                <?php endif; ?>
                <div class="d-flex align-items-center">
                  <i class="fas fa-money-bill-wave me-2" style="color: <?php echo $s['pibg_paid'] ? '#27ae60' : '#e74c3c'; ?>;"></i>
                  <span>Status PIBG:
                    <span class="badge <?php echo $s['pibg_paid'] ? 'bg-success' : 'bg-danger'; ?>">
                      <?php echo $s['pibg_paid'] ? 'Sudah Bayar' : 'Belum Bayar'; ?>
                    </span>
                  </span>
                </div>
              </div>
            <?php endforeach; ?>
          <?php else: ?>
            <div class="text-center" style="padding: 40px;">
              <i class="fas fa-user-plus" style="font-size: 48px; color: #bdc3c7; margin-bottom: 15px;"></i>
              <p style="color: #7f8c8d;">Maklumat anak belum dikemaskini.</p>
              <a href="daftar_anak.php" class="btn btn-primary">
                <i class="fas fa-plus"></i> Daftar Anak
              </a>
            </div>
          <?php endif; ?>
        </div>
      </div>
    </div>
  </div>
</div>

</body>
</html>
