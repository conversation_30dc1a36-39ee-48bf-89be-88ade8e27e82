<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Hash Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 500px;
            margin: 50px auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        input[type="text"], button {
            padding: 10px;
            width: 100%;
            margin-top: 10px;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            background: #e6ffe6;
            border: 1px solid #99cc99;
            word-break: break-all;
        }
    </style>
</head>
<body>

<h2>Generator Hash Kata Laluan (PHP)</h2>

<form method="post">
    <label>Masukkan Kata Laluan:</label>
    <input type="text" name="password" required>
    <button type="submit">Jana Hash</button>
</form>

<?php
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $password = $_POST['password'];
    $hash = password_hash($password, PASSWORD_DEFAULT);
    echo "<div class='result'><strong>Hash:</strong><br>$hash</div>";
}
?>

</body>
</html>
