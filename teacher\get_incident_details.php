<?php
session_start();

// Check authentication
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Teacher') {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

require '../db.php';

// Get teacher ID
$teacher_user_id = $_SESSION['user_id'];
$teacherStmt = $conn->prepare("SELECT teacher_id FROM teachers WHERE user_id = ?");
$teacherStmt->bind_param("i", $teacher_user_id);
$teacherStmt->execute();
$teacher = $teacherStmt->get_result()->fetch_assoc();

if (!$teacher) {
    echo json_encode(['success' => false, 'message' => 'Teacher not found']);
    exit;
}

$teacher_id = $teacher['teacher_id'];

// Get incident ID from request
$incident_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($incident_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid incident ID']);
    exit;
}

try {
    // Get incident details with student information
    $stmt = $conn->prepare("
        SELECT 
            di.*,
            s.full_name as student_name,
            s.no_ic as student_ic,
            c.class_name,
            DATE_FORMAT(di.incident_date, '%d/%m/%Y') as formatted_date,
            DATE_FORMAT(di.created_at, '%d/%m/%Y %H:%i') as formatted_created,
            DATE_FORMAT(di.updated_at, '%d/%m/%Y %H:%i') as formatted_updated
        FROM discipline_incidents di
        JOIN students s ON di.student_id = s.student_id
        LEFT JOIN classrooms c ON s.classroom_id = c.classroom_id
        WHERE di.incident_id = ? AND di.teacher_id = ?
    ");
    
    $stmt->bind_param("ii", $incident_id, $teacher_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Incident not found or access denied']);
        exit;
    }
    
    $incident = $result->fetch_assoc();
    
    // Format the data for display
    $response = [
        'success' => true,
        'incident' => [
            'incident_id' => $incident['incident_id'],
            'student_name' => $incident['student_name'],
            'student_ic' => $incident['student_ic'],
            'class_name' => $incident['class_name'],
            'incident_date' => $incident['incident_date'], // Keep original YYYY-MM-DD format for form input
            'incident_date_display' => $incident['formatted_date'], // Formatted for display
            'incident_type' => $incident['incident_type'],
            'description' => $incident['description'],
            'action_taken' => $incident['action_taken'],
            'severity' => $incident['severity'],
            'status' => $incident['status'],
            'created_at' => $incident['formatted_created'],
            'updated_at' => $incident['formatted_updated']
        ]
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}
?>
