---
title: "SMKTMI School Management System - Analysis & Design Documentation"
subtitle: "Comprehensive System Documentation"
author: "System Analysis Team"
date: "December 2024"
version: "1.0"
geometry: "margin=1in"
fontsize: 11pt
documentclass: article
papersize: a4
toc: true
toc-depth: 3
numbersections: true
highlight-style: github
header-includes:
  - \usepackage{fancyhdr}
  - \usepackage{graphicx}
  - \usepackage{longtable}
  - \usepackage{booktabs}
  - \usepackage{array}
  - \usepackage{multirow}
  - \usepackage{wrapfig}
  - \usepackage{float}
  - \usepackage{colortbl}
  - \usepackage{pdflscape}
  - \usepackage{tabu}
  - \usepackage{threeparttable}
  - \usepackage{threeparttablex}
  - \usepackage{makecell}
  - \pagestyle{fancy}
  - \fancyhead[L]{SMKTMI System Documentation}
  - \fancyhead[R]{Version 1.0}
  - \fancyfoot[C]{\thepage}
  - \renewcommand{\headrulewidth}{0.4pt}
  - \renewcommand{\footrulewidth}{0.4pt}
---

\newpage

# Executive Summary

The SMKTMI (Sekolah Menengah Kebangsaan Tunku Mahmood Iskandar) School Management System is a comprehensive web-based application designed to streamline school operations and enhance communication between administrators, teachers, parents, and students. This document provides detailed system analysis and design specifications for the complete school management solution.

## Key System Features

- **Multi-Role Authentication System** with role-based access control
- **Student Information Management** with parent-child relationships
- **Assessment and Grading System** using Malaysian education standards
- **Attendance Tracking** with real-time reporting
- **Discipline Management** with automated notifications
- **Schedule Management** with printing capabilities
- **PIBG Payment Tracking** for school fees
- **Comprehensive Audit Logging** for system activities

## System Users

| User Role | Primary Functions | Access Level |
|-----------|------------------|--------------|
| **Admin** | System management, user creation, configuration | Full Access |
| **Teacher** | Student management, assessment marking, discipline reports | Functional Access |
| **Parent** | Child registration, progress monitoring | View/Limited Edit |
| **Student** | Academic records viewing, schedule access | View Only |

\newpage

# System Overview

## System Architecture

The SMKTMI system follows a three-tier architecture:

1. **Presentation Layer**: Web-based user interface with role-specific dashboards
2. **Business Logic Layer**: PHP-based application logic with security controls
3. **Data Layer**: MySQL database with normalized table structure

## Technology Stack

- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5
- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Security**: reCAPTCHA, password hashing, session management
- **Email**: PHPMailer for verification and notifications

\newpage

# Context Diagram

The context diagram shows the SMKTMI system as a single process interacting with four external entities. Each entity has specific data flows representing their interactions with the system.

## External Entities and Data Flows

### Admin (System Administrator)
**Data Flows TO System:**
- User management (create, update, delete teachers/students)
- Class and subject management
- Assessment type creation
- Schedule management
- System configuration and settings

**Data Flows FROM System:**
- System reports and statistics
- Activity logs and audit trails
- User lists and management interfaces
- System status and performance metrics

### Teacher (Subject Teacher)
**Data Flows TO System:**
- Student attendance recording
- Assessment marking and grading
- Discipline report creation
- Student information updates
- PIBG payment status updates

**Data Flows FROM System:**
- Student lists and class rosters
- Assessment forms and marking interfaces
- Schedule views and timetables
- Attendance reports and summaries

### Parent (Student Guardian)
**Data Flows TO System:**
- Child registration and enrollment
- Profile updates and contact information
- Information viewing requests

**Data Flows FROM System:**
- Child academic progress reports
- Attendance summaries and records
- Discipline incident notifications
- PIBG payment status
- School announcements and communications

### Student (School Student)
**Data Flows TO System:**
- Profile viewing requests
- Academic record access
- Schedule information requests
- Password change requests

**Data Flows FROM System:**
- Academic results and grades
- Attendance records and summaries
- Class schedule information
- Discipline records (if any)
- Personal academic dashboard

## System Boundaries

The SMKTMI system boundary encompasses:
- **Internal Components**: User authentication, data management, business logic
- **External Interfaces**: Email services, reCAPTCHA verification
- **Data Storage**: MySQL database with all school-related information
- **Security Controls**: Password policies, account lockout, audit logging

\newpage

# Data Flow Diagram Level 0 (System Overview)

The Level 0 DFD decomposes the SMKTMI system into major functional processes, showing how data flows between external entities, processes, and data stores.

## Major Processes

### Process 1.0: User Authentication & Login Management
- **Function**: Validates user credentials and manages login security
- **Inputs**: Login credentials from all user types
- **Outputs**: Authentication results, role-based redirection
- **Data Stores**: D1: Users

### Process 2.0: Student Management
- **Function**: Manages student enrollment, profiles, and academic records
- **Inputs**: Student data from Admin/Parent
- **Outputs**: Student records, class assignments
- **Data Stores**: D2: Students, D5: Classrooms

### Process 3.0: Teacher Management
- **Function**: Manages teacher profiles and subject assignments
- **Inputs**: Teacher data from Admin
- **Outputs**: Teacher records, assignment confirmations
- **Data Stores**: D3: Teachers, D6: Subjects

### Process 4.0: Assessment Management
- **Function**: Creates assessments and processes student results
- **Inputs**: Assessment types from Admin, marks from Teachers
- **Outputs**: Assessment records, grade calculations
- **Data Stores**: D7: Assessment, D8: Assessment Results

### Process 5.0: Attendance Management
- **Function**: Records and tracks student attendance
- **Inputs**: Attendance data from Teachers
- **Outputs**: Attendance records, summary reports
- **Data Stores**: D9: Attendance

### Process 6.0: Discipline Management
- **Function**: Manages disciplinary incidents and reports
- **Inputs**: Incident reports from Teachers
- **Outputs**: Discipline records, notifications
- **Data Stores**: D10: Discipline Incidents

### Process 7.0: Schedule Management
- **Function**: Creates and manages class timetables
- **Inputs**: Schedule data from Admin
- **Outputs**: Timetables, schedule assignments
- **Data Stores**: D11: Schedules

### Process 8.0: PIBG Management
- **Function**: Tracks PIBG payment status
- **Inputs**: Payment updates from Teachers
- **Outputs**: Payment status, reports
- **Data Stores**: D2: Students (pibg_paid field)

### Process 9.0: Admin Logging
- **Function**: Records system activities for audit purposes
- **Inputs**: Activity data from all processes
- **Outputs**: Audit logs, activity reports
- **Data Stores**: D12: Admin Logs

## Data Stores

| Store ID | Name | Description |
|----------|------|-------------|
| D1 | Users | Central authentication table |
| D2 | Students | Student profiles and academic data |
| D3 | Teachers | Teacher profiles and contact information |
| D4 | Parents | Parent profiles and verification status |
| D5 | Classrooms | Class organization and assignments |
| D6 | Subjects | Academic subject definitions |
| D7 | Assessment | Assessment types and schedules |
| D8 | Assessment Results | Student marks and grades |
| D9 | Attendance | Daily attendance records |
| D10 | Discipline Incidents | Disciplinary reports and actions |
| D11 | Schedules | Class timetables and assignments |
| D12 | Admin Logs | System activity audit trail |

\newpage

# Data Flow Diagram Level 1 - Detailed Process Breakdown

## Process 1.0: User Authentication & Login Management

### Sub-Process 1.1: Validate Credentials
**Function**: Verifies user email and password against database records

**Inputs**:
- Email address and password from user
- reCAPTCHA verification token

**Processing**:
- Validate reCAPTCHA response
- Check email existence in users table
- Verify password hash using PHP password_verify()
- Check account lockout status

**Outputs**:
- Authentication success/failure status
- User role information
- Account lockout warnings

**Data Stores Accessed**:
- D1: Users (read user credentials, lockout status)

### Sub-Process 1.2: Handle Login Security
**Function**: Manages failed login attempts and account lockout

**Inputs**:
- Failed authentication attempts
- Current lockout status
- Timestamp information

**Processing**:
- Increment failed_attempts counter
- Check if attempts >= 3
- Set lockout_time if threshold reached
- Calculate 2-minute cooldown period

**Outputs**:
- Updated security status
- Lockout notifications
- Remaining attempt warnings

**Data Stores Accessed**:
- D1: Users (update failed_attempts, lockout_time)

### Sub-Process 1.3: Role-Based Redirection
**Function**: Determines user dashboard based on role_id

**Inputs**:
- Validated user credentials
- Role identification (role_id)
- User profile information

**Processing**:
- Query role-specific table (admins/teachers/parents/students)
- Set session variables
- Determine appropriate dashboard
- Check additional permissions (e.g., parent email verification)

**Outputs**:
- Dashboard redirection
- Session establishment
- Role-specific menu access

**Data Stores Accessed**:
- D2: Students, D3: Teachers, D4: Parents, D1: Admins (read profile data)

## Process 2.0: Parent Registration & Verification

### Sub-Process 2.1: Validate Registration Data
**Function**: Validates parent registration form and enforces business rules

**Inputs**:
- Registration form data (name, email, phone, address)
- Password and confirmation
- Terms acceptance

**Processing**:
- Validate email format and uniqueness
- Enforce password policy (8+ chars, mixed case, digits, symbols)
- Check required field completion
- Validate phone number format

**Outputs**:
- Validation results
- Error messages for corrections
- Approved registration data

**Business Rules Applied**:
- Email must be unique across all users
- Password must meet complexity requirements
- All required fields must be completed

### Sub-Process 2.2: Create User Account
**Function**: Creates user account and parent profile records

**Inputs**:
- Validated registration data
- Hashed password
- Role assignment (role_id = 2)

**Processing**:
- Insert record into users table
- Generate user_id
- Hash password using PHP password_hash()
- Insert parent profile record
- Generate verification token

**Outputs**:
- New user account
- Parent profile record
- Verification token

**Data Stores Accessed**:
- D1: Users (insert new user)
- D4: Parents (insert parent profile)

### Sub-Process 2.3: Send Email Verification
**Function**: Sends verification email with secure token

**Inputs**:
- Parent email address
- Generated verification token
- Token creation timestamp

**Processing**:
- Generate secure verification URL
- Compose verification email
- Send email using PHPMailer
- Set token expiration time

**Outputs**:
- Verification email sent
- Token stored in database
- Confirmation message to user

**External Systems**:
- Email service provider
- PHPMailer library

## Process 3.0: Student Management (Admin Functions)

### Sub-Process 3.1: Validate Student Data
**Function**: Validates student information and checks for duplicates

**Inputs**:
- Student personal information (name, IC number, birth date)
- Parent assignment
- Classroom assignment

**Processing**:
- Validate IC number uniqueness
- Check parent_id existence
- Verify classroom_id validity
- Calculate age from birth date
- Validate gender and relation fields

**Outputs**:
- Validated student data
- Duplicate check results
- Age calculation

**Business Rules**:
- IC number must be unique across all students
- Parent must exist in parents table
- Classroom must exist and have capacity

### Sub-Process 3.2: Create User Account
**Function**: Creates user account for student with role_id = 4

**Inputs**:
- Student email (generated or provided)
- Default or assigned password
- Student profile data

**Processing**:
- Generate unique email if not provided
- Create user account with role_id = 4
- Hash password using security standards
- Link user_id to student record

**Outputs**:
- Student user account
- Login credentials
- Account activation status

### Sub-Process 3.3: Assign to Class
**Function**: Assigns student to classroom and updates enrollment

**Inputs**:
- Student_id
- Classroom_id
- Enrollment date

**Processing**:
- Update student record with classroom_id
- Check classroom capacity
- Update class enrollment count
- Set PIBG payment status to unpaid

**Outputs**:
- Class assignment confirmation
- Updated enrollment records
- PIBG tracking initialization

## Process 4.0: Assessment Management

### Sub-Process 4.1: Create Assessment (Admin)
**Function**: Admin creates assessment types for specific subjects and classes

**Inputs**:
- Assessment type (Ujian 1, Peperiksaan, etc.)
- Target classroom
- Subject selection
- Assessment date

**Processing**:
- Validate assessment type
- Check classroom and subject validity
- Ensure no duplicate assessments
- Set assessment schedule

**Outputs**:
- Assessment records created
- Teacher notification
- Assessment availability

**Data Stores Accessed**:
- D7: Assessment (insert new assessments)
- D5: Classrooms (validate classroom)
- D6: Subjects (validate subjects)

### Sub-Process 4.2: Mark Assessment (Teacher)
**Function**: Teachers input marks for students in their assigned subjects

**Inputs**:
- Assessment_id
- Student marks (0.00-100.00)
- Teacher authentication

**Processing**:
- Verify teacher assignment to subject/classroom
- Validate mark ranges (0-100)
- Calculate grades using Malaysian system
- Update or insert assessment results

**Outputs**:
- Student marks recorded
- Grades calculated
- Assessment completion status

**Grading Calculation**:
- A+ (90-100%), A (80-89%), A- (70-79%)
- B+ (65-69%), B (60-64%), C+ (55-59%)
- C (50-54%), D (45-49%), E (40-44%), G (0-39%)

### Sub-Process 4.3: Calculate Grades
**Function**: Automatically calculates grades based on Malaysian grading system

**Inputs**:
- Student marks (decimal values)
- Grading scale parameters

**Processing**:
- Apply percentage-based grading rules
- Assign appropriate grade letters
- Calculate class statistics
- Generate grade reports

**Outputs**:
- Individual student grades
- Class performance statistics
- Grade distribution reports

\newpage

# Entity Relationship Diagram (ERD)

## Database Schema Overview

The SMKTMI system uses a normalized MySQL database with 14 core tables designed to support all school management functions while maintaining data integrity and performance.

## Core Entities and Relationships

### Primary Entities

#### USERS (Central Authentication)
- **Primary Key**: user_id (INT, AUTO_INCREMENT)
- **Unique Constraints**: email
- **Purpose**: Central authentication table for all system users
- **Relationships**: One-to-one with role-specific tables

#### ADMINS (System Administrators)
- **Primary Key**: admin_id (INT, AUTO_INCREMENT)
- **Foreign Key**: user_id → USERS.user_id
- **Purpose**: Administrator profiles and system access
- **Cardinality**: 1:1 with USERS

#### TEACHERS (Educational Staff)
- **Primary Key**: teacher_id (INT, AUTO_INCREMENT)
- **Foreign Key**: user_id → USERS.user_id
- **Purpose**: Teacher profiles and contact information
- **Cardinality**: 1:1 with USERS, 1:M with CLASSROOMS

#### PARENTS (Student Guardians)
- **Primary Key**: parent_id (INT, AUTO_INCREMENT)
- **Foreign Key**: user_id → USERS.user_id
- **Purpose**: Parent profiles and verification status
- **Cardinality**: 1:1 with USERS, 1:M with STUDENTS

#### STUDENTS (School Students)
- **Primary Key**: student_id (INT, AUTO_INCREMENT)
- **Foreign Keys**:
  - user_id → USERS.user_id
  - parent_id → PARENTS.parent_id
  - classroom_id → CLASSROOMS.classroom_id
- **Purpose**: Student profiles and academic information
- **Cardinality**: 1:1 with USERS, M:1 with PARENTS, M:1 with CLASSROOMS

### Academic Structure Entities

#### CLASSROOMS (Class Organization)
- **Primary Key**: classroom_id (INT, AUTO_INCREMENT)
- **Foreign Key**: teacher_id → TEACHERS.teacher_id
- **Purpose**: Class organization and teacher assignment
- **Cardinality**: M:1 with TEACHERS, 1:M with STUDENTS

#### SUBJECTS (Academic Subjects)
- **Primary Key**: subject_id (INT, AUTO_INCREMENT)
- **Purpose**: Academic subject definitions
- **Common Values**: Bahasa Melayu, Bahasa Inggeris, Matematik, Sains, Sejarah

#### TEACHER_SUBJECT_CLASSROOMS (Assignment Junction)
- **Primary Key**: tsc_id (INT, AUTO_INCREMENT)
- **Foreign Keys**:
  - teacher_id → TEACHERS.teacher_id
  - subject_id → SUBJECTS.subject_id
  - classroom_id → CLASSROOMS.classroom_id
- **Purpose**: Resolves many-to-many relationships between teachers, subjects, and classrooms
- **Cardinality**: M:M:M relationship resolver

### Assessment and Performance Entities

#### ASSESSMENT (Assessment Definitions)
- **Primary Key**: assessment_id (INT, AUTO_INCREMENT)
- **Foreign Keys**:
  - subject_id → SUBJECTS.subject_id
  - classroom_id → CLASSROOMS.classroom_id
- **Purpose**: Assessment/examination definitions
- **Assessment Types**: Ujian 1, Ujian 2, Peperiksaan Pertengahan Tahun, Peperiksaan Akhir Tahun

#### ASSESSMENT_RESULT (Student Results)
- **Primary Key**: result_id (INT, AUTO_INCREMENT)
- **Foreign Keys**:
  - student_id → STUDENTS.student_id
  - assessment_id → ASSESSMENT.assessment_id
- **Purpose**: Student assessment marks and results
- **Unique Constraint**: (student_id, assessment_id)
- **Mark Range**: 0.00 to 100.00 (DECIMAL 5,2)

### Operational Entities

#### ATTENDANCE (Daily Attendance)
- **Primary Key**: attendance_id (INT, AUTO_INCREMENT)
- **Foreign Keys**:
  - student_id → STUDENTS.student_id
  - teacher_id → TEACHERS.teacher_id
- **Purpose**: Daily student attendance tracking
- **Status Values**: Hadir, Tidak Hadir, Lewat

#### DISCIPLINE_INCIDENTS (Disciplinary Records)
- **Primary Key**: incident_id (INT, AUTO_INCREMENT)
- **Foreign Keys**:
  - student_id → STUDENTS.student_id
  - teacher_id → TEACHERS.teacher_id
- **Purpose**: Student disciplinary records and tracking
- **Severity Levels**: Ringan, Sederhana, Berat
- **Status Values**: Baru, Dalam Tindakan, Selesai

#### SCHEDULES (Class Timetables)
- **Primary Key**: schedule_id (INT, AUTO_INCREMENT)
- **Foreign Keys**:
  - teacher_id → TEACHERS.teacher_id
  - subject_id → SUBJECTS.subject_id
  - classroom_id → CLASSROOMS.classroom_id
- **Purpose**: Class timetable and schedule management
- **Days**: Isnin, Selasa, Rabu, Khamis, Jumaat

#### ADMIN_LOGS (Audit Trail)
- **Primary Key**: log_id (INT, AUTO_INCREMENT)
- **Foreign Key**: admin_id → ADMINS.admin_id
- **Purpose**: System activity audit trail
- **Tracking**: All admin activities with detailed descriptions

## Relationship Cardinalities

| Relationship | Cardinality | Description |
|--------------|-------------|-------------|
| USERS ↔ ADMINS | 1:1 | One user account per admin |
| USERS ↔ TEACHERS | 1:1 | One user account per teacher |
| USERS ↔ PARENTS | 1:1 | One user account per parent |
| USERS ↔ STUDENTS | 1:1 | One user account per student |
| PARENTS ↔ STUDENTS | 1:M | One parent can have multiple children |
| TEACHERS ↔ CLASSROOMS | 1:M | One teacher can be class teacher for multiple classes |
| CLASSROOMS ↔ STUDENTS | 1:M | One classroom contains multiple students |
| TEACHERS ↔ TSC | 1:M | One teacher can have multiple subject assignments |
| SUBJECTS ↔ TSC | 1:M | One subject can be taught by multiple teachers |
| CLASSROOMS ↔ TSC | 1:M | One classroom can have multiple teacher-subject combinations |
| SUBJECTS ↔ ASSESSMENT | 1:M | One subject can have multiple assessments |
| CLASSROOMS ↔ ASSESSMENT | 1:M | One classroom can take multiple assessments |
| STUDENTS ↔ ASSESSMENT_RESULT | 1:M | One student can have multiple assessment results |
| ASSESSMENT ↔ ASSESSMENT_RESULT | 1:M | One assessment can have multiple student results |

\newpage

# Data Dictionary

## Table Structures and Specifications

### USERS Table (Central Authentication)

| Column Name | Data Type | Constraints | Description |
|-------------|-----------|-------------|-------------|
| user_id | INT(11) | PRIMARY KEY, AUTO_INCREMENT | Unique user identifier |
| email | VARCHAR(255) | UNIQUE, NOT NULL | User email address |
| password_hash | VARCHAR(255) | NOT NULL | Hashed password using PHP password_hash() |
| role_id | INT(11) | NOT NULL | Role identifier (1=Admin, 2=Parent, 3=Teacher, 4=Student) |
| failed_attempts | INT(11) | DEFAULT 0 | Failed login attempt counter for security |
| lockout_time | DATETIME | NULL | Account lockout timestamp (2-minute duration) |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Record creation timestamp |

**Business Rules:**
- Email must be unique across entire system
- Password must meet policy: 8+ characters, uppercase, lowercase, digit, symbol
- Account locks after 3 failed attempts for 2 minutes
- Role_id determines dashboard redirection and access permissions

### STUDENTS Table (Student Profiles)

| Column Name | Data Type | Constraints | Description |
|-------------|-----------|-------------|-------------|
| student_id | INT(11) | PRIMARY KEY, AUTO_INCREMENT | Unique student identifier |
| user_id | INT(11) | FOREIGN KEY → users.user_id, UNIQUE | Reference to user account |
| parent_id | INT(11) | FOREIGN KEY → parents.parent_id | Reference to parent/guardian |
| classroom_id | INT(11) | FOREIGN KEY → classrooms.classroom_id | Assigned classroom |
| full_name | VARCHAR(255) | NOT NULL | Student full name |
| no_ic | VARCHAR(20) | UNIQUE, NOT NULL | Malaysian Identity Card number |
| gender | ENUM('Lelaki','Perempuan') | NOT NULL | Student gender |
| birth_date | DATE | NOT NULL | Date of birth |
| age | INT(11) | NOT NULL | Current age (calculated from birth_date) |
| relation | VARCHAR(50) | NOT NULL | Relationship to parent (Anak, Anak Angkat, etc.) |
| pibg_paid | TINYINT(1) | DEFAULT 0 | PIBG payment status (0=Unpaid, 1=Paid) |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Record creation timestamp |

**Business Rules:**
- IC number must be unique across all students
- Must be assigned to a valid classroom
- PIBG payment status tracked by teachers
- Age automatically calculated from birth date

### ASSESSMENT Table (Assessment Definitions)

| Column Name | Data Type | Constraints | Description |
|-------------|-----------|-------------|-------------|
| assessment_id | INT(11) | PRIMARY KEY, AUTO_INCREMENT | Unique assessment identifier |
| assessment_type | VARCHAR(100) | NOT NULL | Type of assessment |
| assessment_date | DATE | NOT NULL | Scheduled assessment date |
| subject_id | INT(11) | FOREIGN KEY → subjects.subject_id | Subject being assessed |
| classroom_id | INT(11) | FOREIGN KEY → classrooms.classroom_id | Target classroom |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE | Last modification timestamp |

**Assessment Types:**
- "Ujian 1" - First test
- "Ujian 2" - Second test
- "Peperiksaan Pertengahan Tahun" - Mid-year examination
- "Peperiksaan Akhir Tahun" - Final year examination

**Business Rules:**
- Created by admin, marked by assigned teachers
- One assessment per subject per classroom per assessment type
- Assessment date must be in the future when created

### ASSESSMENT_RESULT Table (Student Results)

| Column Name | Data Type | Constraints | Description |
|-------------|-----------|-------------|-------------|
| result_id | INT(11) | PRIMARY KEY, AUTO_INCREMENT | Unique result identifier |
| student_id | INT(11) | FOREIGN KEY → students.student_id | Student being assessed |
| assessment_id | INT(11) | FOREIGN KEY → assessment.assessment_id | Assessment reference |
| marks | DECIMAL(5,2) | NOT NULL, DEFAULT 0.00 | Marks obtained (0.00-100.00) |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE | Last modification timestamp |

**Business Rules:**
- Marks range from 0.00 to 100.00
- Unique constraint on (student_id, assessment_id) - one result per student per assessment
- Grades calculated automatically using Malaysian grading system
- Only teachers assigned to the subject can enter/modify marks

### DISCIPLINE_INCIDENTS Table (Disciplinary Records)

| Column Name | Data Type | Constraints | Description |
|-------------|-----------|-------------|-------------|
| incident_id | INT(11) | PRIMARY KEY, AUTO_INCREMENT | Unique incident identifier |
| student_id | INT(11) | FOREIGN KEY → students.student_id | Student involved in incident |
| teacher_id | INT(11) | FOREIGN KEY → teachers.teacher_id | Reporting teacher |
| incident_date | DATE | NOT NULL | Date when incident occurred |
| incident_type | VARCHAR(100) | NOT NULL | Type of disciplinary issue |
| description | TEXT | NOT NULL | Detailed incident description (min 10 characters) |
| action_taken | TEXT | NULL | Actions taken by teacher/school |
| severity | ENUM('Ringan','Sederhana','Berat') | DEFAULT 'Ringan' | Severity level |
| status | ENUM('Baru','Dalam Tindakan','Selesai') | DEFAULT 'Baru' | Current status |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE | Last modification timestamp |

**Incident Types:**
- Ponteng Kelas (Skipping Class)
- Tidak Memakai Pakaian Seragam Lengkap (Incomplete Uniform)
- Berkelakuan Tidak Senonoh (Inappropriate Behavior)
- Meniru Semasa Peperiksaan (Cheating During Examination)
- Membawa Telefon Bimbit Tanpa Kebenaran (Unauthorized Mobile Phone)
- Merokok di Kawasan Sekolah (Smoking on School Premises)
- Bergaduh (Fighting)
- Vandalisme (Vandalism)
- Buli (Bullying)

**Business Rules:**
- Any teacher can create reports for any student in the system
- Automatically visible to affected student and parent
- Severity levels help categorize incident seriousness for appropriate response

## Malaysian Grading System

The SMKTMI system implements the standard Malaysian secondary school grading system with automatic grade calculation based on percentage marks.

### Grade Scale

| Grade | Percentage Range | Malay Description | English Description |
|-------|------------------|-------------------|---------------------|
| A+ | 90-100% | Cemerlang Tertinggi | Excellent Plus |
| A | 80-89% | Cemerlang Tinggi | Excellent |
| A- | 70-79% | Cemerlang | Excellent Minus |
| B+ | 65-69% | Kepujian Tertinggi | Credit Plus |
| B | 60-64% | Kepujian Tinggi | Credit |
| C+ | 55-59% | Kepujian Atas | Credit Plus |
| C | 50-54% | Kepujian | Credit |
| D | 45-49% | Lulus Atas | Pass Plus |
| E | 40-44% | Lulus | Pass |
| G | 0-39% | Gagal | Fail |

### Grading Implementation

**Storage**: Marks stored as DECIMAL(5,2) allowing values from 0.00 to 100.00
**Calculation**: Grades calculated in real-time using PHP conditional logic
**Display**: Grades shown in black font without color coding
**Boundaries**: Grade boundaries are fixed and cannot be modified by users

## Security Specifications

### Password Policy

**Requirements:**
- Minimum 8 characters
- At least one uppercase letter (A-Z)
- At least one lowercase letter (a-z)
- At least one digit (0-9)
- At least one special character/symbol
- Regex Pattern: `/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$/`

**Implementation:**
- Enforced during registration and password changes
- Real-time validation with JavaScript
- Server-side validation with PHP
- Password hashing using PHP `password_hash()` function

### Account Security

**Login Security:**
- Account lockout after 3 failed login attempts
- Lockout duration: 2 minutes (120 seconds)
- Failed attempt counter resets on successful login
- reCAPTCHA verification required for all login attempts

**Session Management:**
- Role-based access control
- Session timeout after inactivity
- Secure session handling with PHP sessions
- CSRF protection on all forms

### Data Validation

**Input Validation:**
- Email uniqueness enforced across all users
- IC number uniqueness enforced for students
- SQL injection prevention using prepared statements
- XSS protection through proper output encoding
- Input sanitization for all user data

**Business Rule Enforcement:**
- Database constraints ensure data integrity
- Application-level validation for complex business rules
- Audit logging for all administrative actions
- Regular security monitoring and updates

\newpage

# System Constraints and Implementation Notes

## Technical Requirements

### Server Requirements
- **Web Server**: Apache 2.4+ or Nginx 1.18+
- **PHP Version**: 7.4 or higher
- **Database**: MySQL 5.7+ or MariaDB 10.3+
- **Memory**: Minimum 2GB RAM recommended
- **Storage**: 10GB minimum for application and database

### Client Requirements
- **Browser**: Modern web browser with JavaScript enabled
- **Internet**: Stable internet connection for reCAPTCHA and email verification
- **Resolution**: Minimum 1024x768 screen resolution
- **JavaScript**: Required for interactive features and form validation

## Business Constraints

### Educational Compliance
- Malaysian education system standards compliance
- Bahasa Melayu as primary interface language
- Academic year cycle alignment (January-December)
- Form-based class structure (Tingkatan 1-5)

### Operational Requirements
- PIBG payment tracking integration
- School schedule alignment with Malaysian school hours
- Discipline reporting according to school policies
- Parent-teacher communication facilitation

## Security Constraints

### Access Control
- Role-based permissions strictly enforced
- Password complexity requirements mandatory
- Account lockout policies non-negotiable
- Audit trail maintenance required

### Data Protection
- Personal information encryption in transit
- Secure password storage using industry standards
- Regular security updates and patches
- Backup and recovery procedures

---

**Document Information**

- **Title**: SMKTMI School Management System - Analysis & Design Documentation
- **Version**: 1.0
- **Date**: December 2024
- **Document Type**: System Analysis & Design Specification
- **Prepared For**: SMKTMI School Administration
- **Classification**: Internal Use

**Revision History**

| Version | Date | Author | Description |
|---------|------|--------|-------------|
| 1.0 | December 2024 | System Analysis Team | Initial comprehensive documentation |

---

*This document contains confidential and proprietary information. Distribution is restricted to authorized personnel only.*
