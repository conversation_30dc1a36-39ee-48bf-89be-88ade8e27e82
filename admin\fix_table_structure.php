<?php
session_start();

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';

$message = "";
$action_taken = false;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    
    if ($action === 'add_tsc_id') {
        // Add tsc_id column to existing table
        $alter_sql = "ALTER TABLE teacher_subject_classrooms ADD COLUMN tsc_id INT AUTO_INCREMENT PRIMARY KEY FIRST";
        
        if ($conn->query($alter_sql)) {
            $message = "✅ Successfully added tsc_id column to teacher_subject_classrooms table.";
            $action_taken = true;
        } else {
            $message = "❌ Error adding tsc_id column: " . $conn->error;
        }
    } elseif ($action === 'create_table') {
        // Create the table with proper structure
        $create_sql = "CREATE TABLE teacher_subject_classrooms (
            tsc_id INT AUTO_INCREMENT PRIMARY KEY,
            teacher_id INT NOT NULL,
            subject_id INT NOT NULL,
            classroom_id INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (teacher_id) REFERENCES teachers(teacher_id) ON DELETE CASCADE,
            FOREIGN KEY (subject_id) REFERENCES subjects(subject_id) ON DELETE CASCADE,
            FOREIGN KEY (classroom_id) REFERENCES classrooms(classroom_id) ON DELETE CASCADE,
            UNIQUE KEY unique_assignment (teacher_id, subject_id, classroom_id)
        )";
        
        if ($conn->query($create_sql)) {
            $message = "✅ Successfully created teacher_subject_classrooms table with proper structure.";
            $action_taken = true;
        } else {
            $message = "❌ Error creating table: " . $conn->error;
        }
    }
}

?>
<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Table Structure - SMKTMI</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        .btn-danger { background-color: #dc3545; color: white; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .alert { padding: 15px; margin: 20px 0; border-radius: 4px; }
        .alert-success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .alert-danger { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
    </style>
</head>
<body>

<div class="container">
    <h2>🔧 Fix teacher_subject_classrooms Table Structure</h2>
    
    <?php if ($message): ?>
        <div class="alert <?= $action_taken ? 'alert-success' : 'alert-danger' ?>">
            <?= $message ?>
        </div>
    <?php endif; ?>
    
    <div class="section">
        <h3>Current Table Status</h3>
        <?php
        // Check if table exists
        $table_check = $conn->query("SHOW TABLES LIKE 'teacher_subject_classrooms'");
        if ($table_check && $table_check->num_rows > 0) {
            echo "<p class='success'>✅ Table 'teacher_subject_classrooms' exists</p>";
            
            // Check table structure
            $structure = $conn->query("DESCRIBE teacher_subject_classrooms");
            $has_tsc_id = false;
            $columns = [];
            
            if ($structure) {
                echo "<h4>Table Structure:</h4>";
                echo "<table>";
                echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
                while ($row = $structure->fetch_assoc()) {
                    $columns[] = $row;
                    if ($row['Field'] === 'tsc_id') {
                        $has_tsc_id = true;
                    }
                    echo "<tr>";
                    echo "<td><strong>{$row['Field']}</strong></td>";
                    echo "<td>{$row['Type']}</td>";
                    echo "<td>{$row['Null']}</td>";
                    echo "<td>{$row['Key']}</td>";
                    echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
                    echo "<td>{$row['Extra']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
                
                if ($has_tsc_id) {
                    echo "<p class='success'>✅ Table has tsc_id column - structure is correct!</p>";
                } else {
                    echo "<p class='warning'>⚠️ Table is missing tsc_id column</p>";
                }
            }
            
            // Show row count
            $count_result = $conn->query("SELECT COUNT(*) as count FROM teacher_subject_classrooms");
            if ($count_result) {
                $count = $count_result->fetch_assoc()['count'];
                echo "<p><strong>Total records:</strong> {$count}</p>";
            }
            
        } else {
            echo "<p class='error'>❌ Table 'teacher_subject_classrooms' does not exist</p>";
            $has_tsc_id = false;
        }
        ?>
    </div>
    
    <div class="section">
        <h3>Available Actions</h3>
        
        <?php if ($table_check && $table_check->num_rows > 0): ?>
            <?php if (!$has_tsc_id): ?>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="add_tsc_id">
                    <button type="submit" class="btn btn-warning" onclick="return confirm('This will add tsc_id column to the existing table. Continue?')">
                        🔧 Add tsc_id Column
                    </button>
                </form>
                <p><small>This will add the missing tsc_id column to the existing table.</small></p>
            <?php else: ?>
                <p class='success'>✅ Table structure is correct. No action needed.</p>
            <?php endif; ?>
        <?php else: ?>
            <form method="POST" style="display: inline;">
                <input type="hidden" name="action" value="create_table">
                <button type="submit" class="btn btn-success" onclick="return confirm('This will create the teacher_subject_classrooms table. Continue?')">
                    🆕 Create Table
                </button>
            </form>
            <p><small>This will create the teacher_subject_classrooms table with proper structure.</small></p>
        <?php endif; ?>
        
        <a href="assign_teacher_subject_classroom.php" class="btn btn-primary">
            📚 Go to Assignment Page
        </a>
        
        <a href="check_table_structure.php" class="btn btn-primary">
            🔍 Detailed Table Check
        </a>
    </div>
    
    <?php if ($table_check && $table_check->num_rows > 0): ?>
    <div class="section">
        <h3>Sample Data (First 5 Records)</h3>
        <?php
        $sample = $conn->query("SELECT * FROM teacher_subject_classrooms LIMIT 5");
        if ($sample && $sample->num_rows > 0) {
            echo "<table>";
            $first_row = true;
            while ($row = $sample->fetch_assoc()) {
                if ($first_row) {
                    echo "<tr>";
                    foreach (array_keys($row) as $column) {
                        echo "<th>$column</th>";
                    }
                    echo "</tr>";
                    $first_row = false;
                }
                echo "<tr>";
                foreach ($row as $value) {
                    echo "<td>" . ($value ?? 'NULL') . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='warning'>⚠️ No data found in table</p>";
        }
        ?>
    </div>
    <?php endif; ?>
</div>

</body>
</html>
