<?php
session_start();
require '../db.php';
require '../includes/universal_logger.php';
include 'includes/header.php';
include 'includes/sidebar.php';

$teacher_user_id = $_SESSION['user_id'];
$message = "";

// Dapatkan teacher_id berdasarkan user_id
$stmt = $conn->prepare("SELECT teacher_id FROM teachers WHERE user_id = ?");
$stmt->bind_param("i", $teacher_user_id);
$stmt->execute();
$teacher = $stmt->get_result()->fetch_assoc();
$teacher_id = $teacher['teacher_id'] ?? 0;

// Dapatkan classroom yang diajar oleh guru ini
$stmt2 = $conn->prepare("SELECT classroom_id, class_name FROM classrooms WHERE teacher_id = ?");
$stmt2->bind_param("i", $teacher_id);
$stmt2->execute();
$classroom = $stmt2->get_result()->fetch_assoc();

if (!$classroom) {
    echo "<div class='alert alert-danger m-4'>❌ Anda bukan guru kelas. Anda tidak dibenarkan mengambil kehadiran.</div>";
    exit;
}

$classroom_id = $classroom['classroom_id'];
$class_name = $classroom['class_name'];
$today = date('Y-m-d');

// Dapatkan senarai pelajar dalam kelas
$stmt3 = $conn->prepare("SELECT student_id, full_name FROM students WHERE classroom_id = ? ORDER BY full_name ASC");
$stmt3->bind_param("i", $classroom_id);
$stmt3->execute();
$students = $stmt3->get_result()->fetch_all(MYSQLI_ASSOC);

// Dapatkan kehadiran hari ini yang sudah direkod dalam array associative student_id => status
$existingStatus = [];
$stmt4 = $conn->prepare("SELECT student_id, status FROM attendance WHERE classroom_id = ? AND attendance_date = ?");
$stmt4->bind_param("is", $classroom_id, $today);
$stmt4->execute();
$res4 = $stmt4->get_result();
while ($row = $res4->fetch_assoc()) {
    $existingStatus[$row['student_id']] = $row['status'];
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['attendance'])) {
    foreach ($_POST['attendance'] as $student_id => $status) {
        if (!in_array($status, ['Hadir', 'Tidak Hadir'])) continue;

        $checkStmt = $conn->prepare("SELECT attendance_id FROM attendance WHERE student_id = ? AND attendance_date = ?");
        $checkStmt->bind_param("is", $student_id, $today);
        $checkStmt->execute();
        $existing = $checkStmt->get_result()->fetch_assoc();

        if ($existing) {
            $updateStmt = $conn->prepare("UPDATE attendance SET status = ? WHERE attendance_id = ?");
            $updateStmt->bind_param("si", $status, $existing['attendance_id']);
            $updateStmt->execute();
        } else {
            $insertStmt = $conn->prepare("INSERT INTO attendance (student_id, teacher_id, classroom_id, attendance_date, status) VALUES (?, ?, ?, ?, ?)");
            $insertStmt->bind_param("iiiss", $student_id, $teacher_id, $classroom_id, $today, $status);
            $insertStmt->execute();
        }
    }

    // Log the attendance recording with details
    $class_name = '';
    $class_query = $conn->prepare("SELECT class_name FROM classrooms WHERE classroom_id = ?");
    $class_query->bind_param("i", $classroom_id);
    $class_query->execute();
    $class_result = $class_query->get_result();
    if ($class_row = $class_result->fetch_assoc()) {
        $class_name = $class_row['class_name'];
    }
    $class_query->close();

    // Count attendance status
    $present_count = 0;
    $absent_count = 0;
    foreach ($_POST['attendance'] as $status) {
        if ($status === 'Hadir') {
            $present_count++;
        } else {
            $absent_count++;
        }
    }

    $details = [
        'Kelas' => $class_name,
        'Tarikh' => $today,
        'Jumlah Pelajar' => ($present_count + $absent_count),
        'Hadir' => $present_count,
        'Tidak Hadir' => $absent_count
    ];

    logUserActivity($conn, 'ATTENDANCE', 'CLASSROOM', $classroom_id, $class_name,
        "Kehadiran direkodkan untuk kelas $class_name", $details);

    $message = "✅ Kehadiran berjaya direkodkan untuk hari ini.";

    $existingStatus = [];
    $stmt4->execute();
    $res4 = $stmt4->get_result();
    while ($row = $res4->fetch_assoc()) {
        $existingStatus[$row['student_id']] = $row['status'];
    }
}

$attendanceToday = [];
$stmt5 = $conn->prepare("SELECT s.full_name, a.status FROM attendance a JOIN students s ON a.student_id = s.student_id WHERE a.classroom_id = ? AND a.attendance_date = ?");
$stmt5->bind_param("is", $classroom_id, $today);
$stmt5->execute();
$result5 = $stmt5->get_result();
while ($row = $result5->fetch_assoc()) {
    $attendanceToday[] = $row;
}
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rekod Kehadiran Pelajar - SMKTMI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>

<style>
/* Content Styling */
.content {
    margin-left: 280px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.form-container {
    width: 100%;
    max-width: 1200px;
    background-color: #fff;
    padding: 30px 40px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0 auto;
}

.form-container h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 25px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    color: #2980b9;
    text-align: center;
}

.alert {
    padding: 15px;
    background-color: #d9edf7;
    border: 1px solid #bce8f1;
    border-radius: 5px;
    color: #31708f;
    margin-bottom: 25px;
    text-align: center;
}

.alert-success {
    background-color: #dff0d8;
    border-color: #d6e9c6;
    color: #3c763d;
}

.alert-warning {
    background-color: #fcf8e3;
    border-color: #faebcc;
    color: #8a6d3b;
}

.stats-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-left: 4px solid #3498db;
    transition: transform 0.2s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-primary {
    background-color: #3498db;
    border-color: #3498db;
    padding: 10px 30px;
    font-weight: 600;
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

.badge {
    padding: 6px 10px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

/* Responsive */
@media (max-width: 768px) {
    .content {
        margin-left: 0;
        padding: 20px;
    }
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
  <div class="form-container">
    <h2><i class="fas fa-calendar-check"></i> Rekod Kehadiran Pelajar</h2>

    <?php if (!empty($message)): ?>
      <div class="alert alert-success">
        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
      </div>
    <?php endif; ?>

    <div class="row">
      <div class="col-md-6">
        <div class="stats-card">
          <h5 style="margin-bottom: 20px; color: #2c3e50; text-align: center;">
            <i class="fas fa-user-check"></i> Ambil Kehadiran Hari Ini
          </h5>
          <div class="text-center mb-3">
            <span class="badge bg-primary" style="font-size: 14px; padding: 8px 12px;">
              <i class="fas fa-calendar"></i> <?php echo date('d/m/Y'); ?>
            </span>
          </div>

          <form method="post" id="attendanceForm">
            <table class="table table-bordered">
              <thead class="table-light">
                <tr>
                  <th>Nama Pelajar</th>
                  <th width="150">Status</th>
                </tr>
              </thead>
              <tbody>
                <?php foreach ($students as $student):
                    $sid = $student['student_id'];
                    $currentStatus = $existingStatus[$sid] ?? '';
                ?>
                  <tr>
                    <td><?php echo htmlspecialchars($student['full_name']); ?></td>
                    <td>
                      <select name="attendance[<?php echo $sid; ?>]" class="form-select attendance-select">
                        <option value="" disabled <?php echo $currentStatus === '' ? 'selected' : ''; ?>>Sila Pilih</option>
                        <option value="Hadir" <?php echo $currentStatus === 'Hadir' ? 'selected' : ''; ?>>Hadir</option>
                        <option value="Tidak Hadir" <?php echo $currentStatus === 'Tidak Hadir' ? 'selected' : ''; ?>>Tidak Hadir</option>
                      </select>
                    </td>
                  </tr>
                <?php endforeach; ?>
              </tbody>
            </table>
            <div class="text-center">
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> Simpan Kehadiran
              </button>
            </div>
          </form>
        </div>
      </div>

      <div class="col-md-6">
        <div class="stats-card">
          <h5 style="margin-bottom: 20px; color: #2c3e50; text-align: center;">
            <i class="fas fa-list-check"></i> Senarai Kehadiran Hari Ini
          </h5>

          <?php if (empty($attendanceToday)): ?>
            <div class="alert alert-warning">
              <i class="fas fa-exclamation-triangle"></i> Tiada rekod kehadiran untuk hari ini.
            </div>
          <?php else: ?>
            <?php
                $studentCount = count($students);
                $attendanceCount = count($attendanceToday);
                if ($attendanceCount < $studentCount):
            ?>
              <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                Kehadiran belum lengkap. Ada <?php echo $studentCount - $attendanceCount; ?> pelajar belum direkodkan.
              </div>
            <?php endif; ?>

            <table class="table table-bordered">
              <thead class="table-light">
                <tr>
                  <th>Nama Pelajar</th>
                  <th width="120">Status</th>
                </tr>
              </thead>
              <tbody>
                <?php foreach ($attendanceToday as $row):
                    $status = $row['status'];
                    $badgeClass = $status === 'Hadir' ? 'success' : 'danger';
                    $icon = $status === 'Hadir' ? 'check-circle' : 'times-circle';
                ?>
                  <tr>
                    <td><?php echo htmlspecialchars($row['full_name']); ?></td>
                    <td>
                      <span class="badge bg-<?php echo $badgeClass; ?>">
                        <i class="fas fa-<?php echo $icon; ?>"></i> <?php echo $status; ?>
                      </span>
                    </td>
                  </tr>
                <?php endforeach; ?>
              </tbody>
            </table>

            <div class="text-center">
              <span class="badge bg-info" style="font-size: 14px; padding: 8px 12px;">
                <i class="fas fa-users"></i>
                Jumlah Direkod: <?php echo $attendanceCount; ?>/<?php echo $studentCount; ?> Pelajar
              </span>
            </div>
          <?php endif; ?>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.getElementById('attendanceForm').addEventListener('submit', function(e) {
    const selects = this.querySelectorAll('select.attendance-select');
    for (const sel of selects) {
        if (sel.value === '') {
            alert('Sila pilih status kehadiran untuk semua pelajar sebelum menyimpan.');
            sel.focus();
            e.preventDefault();
            return false;
        }
    }
});
</script>
</body>
</html>
