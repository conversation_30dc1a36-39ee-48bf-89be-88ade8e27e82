<?php
require '../db.php';
require 'includes/header.php';
//require 'includes/sidebar.php';

$user_id = $_SESSION['user_id'];

// Semak guru ada kelas
$stmt = $conn->prepare("SELECT classroom_id, class_name FROM classrooms WHERE teacher_id = (
    SELECT teacher_id FROM teachers WHERE user_id = ?
)");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();

/*if ($result->num_rows === 0) {
    echo "<script>
        alert('Anda belum ditetapkan sebagai guru kelas. Sila hubungi pentadbir sistem.');
        window.location.href = 'dashboard.php';
    </script>";
    exit;
}*/ // Baik pakai script ini nanti jangan pakai script die

if ($result->num_rows === 0) {
    die("<div class='content'><h4>Anda belum ditetapkan sebagai guru kelas. Sila hubungi pentadbir sistem.</h4></div>");
}

$class_info = $result->fetch_assoc();
$classroom_id = $class_info['classroom_id'];
$class_name = $class_info['class_name'];

// Umur disasarkan berdasarkan Tingkatan
$umur_disasarkan = null;
if (preg_match('/Tingkatan\s*(\d+)/i', $class_name, $matches)) {
    $tingkatan = (int)$matches[1];
    $umur_disasarkan = 12 + $tingkatan;
} else {
    die("<div class='content'><h3>Nama kelas tidak sah. Guna format 'Tingkatan X'.</h3></div>");
}

// Jika borang dihantar
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['student_ids'])) {
    $selected_ids = $_POST['student_ids'];
    $placeholders = implode(',', array_fill(0, count($selected_ids), '?'));
    $types = str_repeat('i', count($selected_ids));

    $stmt = $conn->prepare("UPDATE students SET classroom_id = ? WHERE student_id IN ($placeholders)");
    $params = array_merge([$classroom_id], $selected_ids);
    $stmt->bind_param('i' . $types, ...$params);

    if ($stmt->execute()) {
        $msg = "Pendaftaran pelajar ke dalam kelas berjaya dilaksana.";
    } else {
        $msg = "Ralat: " . $stmt->error;
    }
}

// Dapatkan pelajar belum berkelas ikut umur
$stmt = $conn->prepare("SELECT student_id, full_name, no_ic FROM students WHERE age = ? AND classroom_id IS NULL ORDER BY full_name ASC");
$stmt->bind_param("i", $umur_disasarkan);
$stmt->execute();
$students_unassigned = $stmt->get_result();

// Dapatkan pelajar yang telah didaftar ke kelas ini
$stmt = $conn->prepare("SELECT full_name, no_ic FROM students WHERE classroom_id = ? ORDER BY full_name ASC");
$stmt->bind_param("i", $classroom_id);
$stmt->execute();
$students_assigned = $stmt->get_result();
$total_assigned = $students_assigned->num_rows;
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tetapan Pelajar - Kelas - SMKTMI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>

<?php include 'includes/sidebar.php'; ?>

<style>
/* Content Styling */
.content {
    margin-left: 280px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.form-container {
    width: 100%;
    max-width: 1200px;
    background-color: #fff;
    padding: 30px 40px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0 auto;
}

.form-container h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 25px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    color: #2980b9;
    text-align: center;
}

.alert {
    padding: 15px;
    background-color: #d9edf7;
    border: 1px solid #bce8f1;
    border-radius: 5px;
    color: #31708f;
    margin-bottom: 25px;
    text-align: center;
}

.alert-success {
    background-color: #dff0d8;
    border-color: #d6e9c6;
    color: #3c763d;
}

.alert-warning {
    background-color: #fcf8e3;
    border-color: #faebcc;
    color: #8a6d3b;
}

.stats-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-left: 4px solid #3498db;
}

.btn-primary {
    background-color: #3498db;
    border-color: #3498db;
    padding: 10px 30px;
    font-weight: 600;
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

/* Responsive */
@media (max-width: 768px) {
    .content {
        margin-left: 0;
        padding: 20px;
    }
}
</style>

<div class="content">
  <div class="form-container">
    <h2><i class="fas fa-users"></i> Tetapan Pelajar - Kelas</h2>

    <?php if (!empty($msg)): ?>
      <div class="alert alert-success">
        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($msg); ?>
      </div>
    <?php endif; ?>

    <div class="row">
      <!-- Left Column: Unassigned Students -->
      <div class="col-md-6">
        <div class="stats-card">
          <h5 style="margin-bottom: 20px; color: #2c3e50; text-align: center;">
            <i class="fas fa-user-plus"></i> Senarai Pelajar Belum Didaftarkan
          </h5>

          <?php if ($students_unassigned->num_rows > 0): ?>
            <form method="POST">
              <table class="table table-bordered">
                <thead class="table-light">
                  <tr>
                    <th width="60">Pilih</th>
                    <th>Nama</th>
                    <th>No Kad Pengenalan</th>
                  </tr>
                </thead>
                <tbody>
                  <?php while ($row = $students_unassigned->fetch_assoc()): ?>
                    <tr>
                      <td class="text-center">
                        <input type="checkbox" class="form-check-input" name="student_ids[]"
                               value="<?php echo $row['student_id']; ?>">
                      </td>
                      <td><?php echo htmlspecialchars($row['full_name']); ?></td>
                      <td><?php echo htmlspecialchars($row['no_ic']); ?></td>
                    </tr>
                  <?php endwhile; ?>
                </tbody>
              </table>
              <div class="text-center">
                <button type="submit" class="btn btn-primary">
                  <i class="fas fa-user-plus"></i> Daftar Pelajar
                </button>
              </div>
            </form>
          <?php else: ?>
            <div class="alert alert-warning">
              <i class="fas fa-info-circle"></i> Semua pelajar telah didaftarkan.
            </div>
          <?php endif; ?>
        </div>
      </div>

      <!-- Right Column: Assigned Students -->
      <div class="col-md-6">
        <div class="stats-card">
          <h5 style="margin-bottom: 20px; color: #2c3e50; text-align: center;">
            <i class="fas fa-users"></i> Senarai Pelajar Telah Didaftarkan
          </h5>

          <?php if ($total_assigned > 0): ?>
            <table class="table table-bordered">
              <thead class="table-light">
                <tr>
                  <th width="60">Bil.</th>
                  <th>Nama</th>
                  <th>No Kad Pengenalan</th>
                </tr>
              </thead>
              <tbody>
                <?php $bil = 1; while ($row = $students_assigned->fetch_assoc()): ?>
                  <tr>
                    <td class="text-center"><?php echo $bil++; ?></td>
                    <td><?php echo htmlspecialchars($row['full_name']); ?></td>
                    <td><?php echo htmlspecialchars($row['no_ic']); ?></td>
                  </tr>
                <?php endwhile; ?>
              </tbody>
            </table>
            <div class="text-center">
              <span class="badge bg-success" style="font-size: 14px; padding: 8px 12px;">
                <i class="fas fa-users"></i> Jumlah Pelajar: <?php echo $total_assigned; ?> Orang
              </span>
            </div>
          <?php else: ?>
            <div class="alert alert-warning">
              <i class="fas fa-exclamation-triangle"></i> Tiada pelajar dalam kelas ini.
            </div>
          <?php endif; ?>
        </div>
      </div>
    </div>
  </div>
</div>

</body>
</html>
