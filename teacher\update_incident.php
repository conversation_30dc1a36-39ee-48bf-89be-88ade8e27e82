<?php
session_start();

// Check authentication
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Teacher') {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

require '../db.php';

// Get teacher ID
$teacher_user_id = $_SESSION['user_id'];
$teacherStmt = $conn->prepare("SELECT teacher_id FROM teachers WHERE user_id = ?");
$teacherStmt->bind_param("i", $teacher_user_id);
$teacherStmt->execute();
$teacher = $teacherStmt->get_result()->fetch_assoc();

if (!$teacher) {
    echo json_encode(['success' => false, 'message' => 'Teacher not found']);
    exit;
}

$teacher_id = $teacher['teacher_id'];

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Get form data
$incident_id = isset($_POST['incident_id']) ? (int)$_POST['incident_id'] : 0;
$incident_date = isset($_POST['incident_date']) ? trim($_POST['incident_date']) : '';
$incident_type = isset($_POST['incident_type']) ? trim($_POST['incident_type']) : '';
$severity = isset($_POST['severity']) ? trim($_POST['severity']) : '';
$status = isset($_POST['status']) ? trim($_POST['status']) : '';
$description = isset($_POST['description']) ? trim($_POST['description']) : '';
$action_taken = isset($_POST['action_taken']) ? trim($_POST['action_taken']) : '';

// Debug: Log received data (remove in production)
error_log("Update incident data: " . json_encode($_POST));

// Validate input
if ($incident_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid incident ID']);
    exit;
}

if (empty($incident_date) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $incident_date)) {
    echo json_encode(['success' => false, 'message' => 'Valid incident date is required (YYYY-MM-DD format)']);
    exit;
}

if (empty($incident_type)) {
    echo json_encode(['success' => false, 'message' => 'Incident type is required']);
    exit;
}

if (empty($description) || strlen(trim($description)) < 10) {
    echo json_encode(['success' => false, 'message' => 'Description must be at least 10 characters']);
    exit;
}

if (empty($severity) || !in_array($severity, ['Ringan', 'Sederhana', 'Berat'])) {
    echo json_encode(['success' => false, 'message' => 'Valid severity level is required']);
    exit;
}

if (empty($status) || !in_array($status, ['Baru', 'Dalam Tindakan', 'Selesai'])) {
    echo json_encode(['success' => false, 'message' => 'Valid status is required']);
    exit;
}

try {
    // First verify that this incident belongs to the current teacher
    $verifyStmt = $conn->prepare("SELECT incident_id FROM discipline_incidents WHERE incident_id = ? AND teacher_id = ?");
    $verifyStmt->bind_param("ii", $incident_id, $teacher_id);
    $verifyStmt->execute();
    $verifyResult = $verifyStmt->get_result();
    
    if ($verifyResult->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Incident not found or access denied']);
        exit;
    }
    
    // Check which columns exist in the table
    $columns = $conn->query("SHOW COLUMNS FROM discipline_incidents");
    $available_columns = [];
    while ($col = $columns->fetch_assoc()) {
        $available_columns[] = $col['Field'];
    }
    
    // Build dynamic update query based on available columns
    $update_fields = [
        'incident_date = ?',
        'incident_type = ?',
        'description = ?'
    ];
    $update_values = [$incident_date, $incident_type, $description];
    $param_types = 'sss';
    
    // Add optional columns if they exist
    if (in_array('action_taken', $available_columns)) {
        $update_fields[] = 'action_taken = ?';
        $update_values[] = $action_taken;
        $param_types .= 's';
    }
    
    if (in_array('severity', $available_columns)) {
        $update_fields[] = 'severity = ?';
        $update_values[] = $severity;
        $param_types .= 's';
    }
    
    if (in_array('status', $available_columns)) {
        $update_fields[] = 'status = ?';
        $update_values[] = $status;
        $param_types .= 's';
    }
    
    if (in_array('updated_at', $available_columns)) {
        $update_fields[] = 'updated_at = NOW()';
    }
    
    // Add incident_id and teacher_id for WHERE clause
    $update_values[] = $incident_id;
    $update_values[] = $teacher_id;
    $param_types .= 'ii';
    
    // Build the SQL query
    $fields_str = implode(', ', $update_fields);
    $updateSQL = "UPDATE discipline_incidents SET $fields_str WHERE incident_id = ? AND teacher_id = ?";
    
    $updateStmt = $conn->prepare($updateSQL);
    
    if (!$updateStmt) {
        echo json_encode(['success' => false, 'message' => 'Failed to prepare update query: ' . $conn->error]);
        exit;
    }
    
    $updateStmt->bind_param($param_types, ...$update_values);
    
    if ($updateStmt->execute()) {
        if ($updateStmt->affected_rows > 0) {
            echo json_encode(['success' => true, 'message' => 'Incident updated successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'No changes were made']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update incident: ' . $updateStmt->error]);
    }
    
    $updateStmt->close();
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}
?>
