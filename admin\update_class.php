<?php
// update_class.php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

$host = "localhost";
$dbname = "smktmi-v6";
$username = "root";
$password = "";

$conn = new mysqli($host, $username, $password, $dbname);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

$class_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
if ($class_id <= 0) {
    die("Invalid class ID.");
}

$errors = [];
$success = "";

// Fetch existing class data
$stmt = $conn->prepare("SELECT class_name, location FROM sis_classroom WHERE class_id = ?");
$stmt->bind_param("i", $class_id);
$stmt->execute();
$result = $stmt->get_result();
if ($result->num_rows != 1) {
    die("Class not found.");
}
$class = $result->fetch_assoc();
$stmt->close();

// Define valid locations for dropdown
$valid_locations = ['Blok A', 'Blok B', 'Blok C'];

// Handle form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $class_name = trim($_POST['class_name']);
    $location = $_POST['location'];

    // Validate
    if (empty($class_name)) $errors[] = "Class name is required.";
    if (!in_array($location, $valid_locations)) $errors[] = "Invalid location selected.";

    // Optionally check if class_name is unique (excluding current class)
    $stmt = $conn->prepare("SELECT class_id FROM sis_classroom WHERE class_name = ? AND class_id != ?");
    $stmt->bind_param("si", $class_name, $class_id);
    $stmt->execute();
    $stmt->store_result();
    if ($stmt->num_rows > 0) {
        $errors[] = "Class name already exists.";
    }
    $stmt->close();

    if (empty($errors)) {
        // Update DB
        $stmt = $conn->prepare("UPDATE sis_classroom SET class_name=?, location=? WHERE class_id=?");
        $stmt->bind_param("ssi", $class_name, $location, $class_id);
        if ($stmt->execute()) {
            $success = "Class information updated successfully.";
            $class['class_name'] = $class_name;
            $class['location'] = $location;
        } else {
            $errors[] = "Failed to update class: " . $stmt->error;
        }
        $stmt->close();
    }
}

$conn->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<title>Update Class</title>
<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    label { display: block; margin-top: 10px; }
    input[type="text"], select {
        width: 300px; padding: 6px; margin-top: 4px;
    }
    .error { color: red; }
    .success { color: green; }
    button { margin-top: 15px; padding: 8px 16px; }
    .button-group { margin-top: 15px; }
    .btn-cancel {
        background-color: #e74c3c;
        color: white;
        padding: 8px 16px;
        text-decoration: none;
        border-radius: 4px;
        margin-left: 10px;
    }
    .btn-cancel:hover {
        background-color: #c0392b;
        color: white;
        text-decoration: none;
    }
</style>
</head>
<body>

<h2>Update Class Information</h2>

<?php
if ($success) {
    echo "<p class='success'>{$success}</p>";
}
if ($errors) {
    echo "<ul class='error'>";
    foreach ($errors as $err) {
        echo "<li>" . htmlspecialchars($err) . "</li>";
    }
    echo "</ul>";
}
?>

<form method="post" action="">
    <label>Class Name:
        <input type="text" name="class_name" value="<?= htmlspecialchars($class['class_name']) ?>" required />
    </label>
    <label>Location:
        <select name="location" required>
            <option value="">-- Select Location --</option>
            <?php foreach ($valid_locations as $loc): ?>
                <option value="<?= htmlspecialchars($loc) ?>" <?= $class['location'] == $loc ? 'selected' : '' ?>><?= htmlspecialchars($loc) ?></option>
            <?php endforeach; ?>
        </select>
    </label>

    <div class="button-group">
        <button type="submit">Update Class</button>
        <a href="admin_dashboard.php" class="btn-cancel">Cancel</a>
    </div>
</form>

</body>
</html>
