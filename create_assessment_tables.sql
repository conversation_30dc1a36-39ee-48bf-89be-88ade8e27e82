
USE `smktmi-v6`;

CREATE TABLE IF NOT EXISTS `assessment` (
  `assessment_id` int(11) NOT NULL AUTO_INCREMENT,
  `assessment_type` varchar(100) NOT NULL COMMENT 'Type of assessment (e.g., <PERSON>jian 1, <PERSON><PERSON><PERSON><PERSON><PERSON>)',
  `assessment_date` date NOT NULL COMMENT 'Date of the assessment',
  `subject_id` int(11) NOT NULL COMMENT 'Foreign key to subjects table',
  `classroom_id` int(11) NOT NULL COMMENT 'Foreign key to classrooms table',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`assessment_id`),
  KEY `idx_subject_id` (`subject_id`),
  KEY `idx_classroom_id` (`classroom_id`),
  KEY `idx_assessment_date` (`assessment_date`),
  CONSTRAINT `fk_assessment_subject` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`subject_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_assessment_classroom` FOREIGN KEY (`classroom_id`) REFERENCES `classrooms` (`classroom_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Assessment/Exam information table';


CREATE TABLE IF NOT EXISTS `assessment_result` (
  `result_id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL COMMENT 'Foreign key to students table',
  `assessment_id` int(11) NOT NULL COMMENT 'Foreign key to assessment table',
  `marks` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT 'Marks obtained by student (0.00 to 100.00)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`result_id`),
  UNIQUE KEY `unique_student_assessment` (`student_id`, `assessment_id`),
  KEY `idx_student_id` (`student_id`),
  KEY `idx_assessment_id` (`assessment_id`),
  KEY `idx_marks` (`marks`),
  CONSTRAINT `fk_result_student` FOREIGN KEY (`student_id`) REFERENCES `students` (`student_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_result_assessment` FOREIGN KEY (`assessment_id`) REFERENCES `assessment` (`assessment_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Student assessment results table';


INSERT INTO `assessment` (`assessment_type`, `assessment_date`, `subject_id`, `classroom_id`) VALUES
('Ujian 1', '2024-01-15', 1, 1),
('Peperiksaan Pertengahan Tahun', '2024-03-15', 1, 1),
('Ujian 2', '2024-05-15', 2, 1),
('Peperiksaan Akhir Tahun', '2024-11-15', 1, 1);



SHOW TABLES LIKE '%assessment%';


DESCRIBE assessment;
DESCRIBE assessment_result;


SELECT 
    a.assessment_id,
    a.assessment_type,
    a.assessment_date,
    s.subject_name,
    c.class_name
FROM assessment a
LEFT JOIN subjects s ON a.subject_id = s.subject_id
LEFT JOIN classrooms c ON a.classroom_id = c.classroom_id
ORDER BY a.assessment_date DESC;
