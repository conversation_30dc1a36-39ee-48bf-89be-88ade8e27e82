<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SMKTMI School Management System - Analysis & Design Documentation</title>
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            margin: 0;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }

        /* Header styles */
        .document-header {
            background: linear-gradient(135deg, #2980b9, #3498db);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .document-header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .document-header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .document-info {
            display: flex;
            justify-content: space-around;
            margin-top: 20px;
            font-size: 0.9em;
        }

        .document-info div {
            text-align: center;
        }

        /* Navigation styles */
        .nav-container {
            background: #34495e;
            padding: 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            overflow-x: auto;
        }

        .nav-menu li {
            white-space: nowrap;
        }

        .nav-menu a {
            display: block;
            padding: 15px 20px;
            color: white;
            text-decoration: none;
            transition: background-color 0.3s;
            border-right: 1px solid #2c3e50;
        }

        .nav-menu a:hover {
            background-color: #2c3e50;
        }

        /* Content styles */
        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 50px;
            scroll-margin-top: 80px;
        }

        h1, h2, h3, h4, h5, h6 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-weight: 600;
        }

        h1 {
            font-size: 2.2em;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }

        h2 {
            font-size: 1.8em;
            color: #2980b9;
            margin-top: 40px;
        }

        h3 {
            font-size: 1.4em;
            color: #34495e;
            margin-top: 30px;
        }

        h4 {
            font-size: 1.2em;
            color: #5d6d7e;
            margin-top: 25px;
        }

        /* Table styles */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }

        th {
            background: linear-gradient(135deg, #34495e, #2c3e50);
            color: white;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        td {
            padding: 12px;
            border-bottom: 1px solid #ecf0f1;
            vertical-align: top;
        }

        tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        tr:hover {
            background-color: #e8f4fd;
        }

        /* Code and pre styles */
        code {
            background: #f1f2f6;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            color: #e74c3c;
        }

        pre {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.4;
        }

        pre code {
            background: none;
            color: inherit;
            padding: 0;
        }

        /* List styles */
        ul, ol {
            margin: 15px 0;
            padding-left: 30px;
        }

        li {
            margin: 8px 0;
        }

        /* Info boxes */
        .info-box {
            background: linear-gradient(135deg, #e8f4fd, #d6eaf8);
            border-left: 5px solid #3498db;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }

        .warning-box {
            background: linear-gradient(135deg, #fef9e7, #fcf3cf);
            border-left: 5px solid #f39c12;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }

        .success-box {
            background: linear-gradient(135deg, #eafaf1, #d5f4e6);
            border-left: 5px solid #27ae60;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }

        /* Diagram styles */
        .diagram {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.8em;
            line-height: 1.2;
            overflow-x: auto;
            white-space: pre;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .document-header {
                padding: 20px;
            }

            .document-header h1 {
                font-size: 2em;
            }

            .content {
                padding: 20px;
            }

            .nav-menu {
                flex-direction: column;
            }

            .nav-menu a {
                border-right: none;
                border-bottom: 1px solid #2c3e50;
            }

            table {
                font-size: 0.8em;
            }

            th, td {
                padding: 8px;
            }
        }

        /* Print styles */
        @media print {
            body {
                background: white;
                padding: 0;
            }

            .container {
                box-shadow: none;
                border-radius: 0;
            }

            .nav-container {
                display: none;
            }

            .section {
                page-break-inside: avoid;
            }

            h1, h2, h3 {
                page-break-after: avoid;
            }

            table {
                page-break-inside: avoid;
            }
        }

        /* Table of Contents */
        .toc {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .toc h3 {
            color: #2980b9;
            margin-bottom: 15px;
            margin-top: 0;
        }

        .toc ul {
            list-style: none;
            padding-left: 0;
        }

        .toc li {
            margin: 8px 0;
        }

        .toc a {
            color: #34495e;
            text-decoration: none;
            padding: 5px 0;
            display: block;
            border-bottom: 1px dotted #bdc3c7;
            transition: color 0.3s;
        }

        .toc a:hover {
            color: #2980b9;
        }

        .toc .toc-level-2 {
            padding-left: 20px;
        }

        .toc .toc-level-3 {
            padding-left: 40px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Document Header -->
        <header class="document-header">
            <h1>SMKTMI School Management System</h1>
            <div class="subtitle">Comprehensive System Analysis & Design Documentation</div>
            <div class="document-info">
                <div>
                    <strong>Version:</strong> 1.0
                </div>
                <div>
                    <strong>Date:</strong> December 2024
                </div>
                <div>
                    <strong>Type:</strong> System Documentation
                </div>
                <div>
                    <strong>Classification:</strong> Internal Use
                </div>
            </div>
        </header>

        <!-- Navigation Menu -->
        <nav class="nav-container">
            <ul class="nav-menu">
                <li><a href="#executive-summary">Executive Summary</a></li>
                <li><a href="#context-diagram">Context Diagram</a></li>
                <li><a href="#dfd-level-0">DFD Level 0</a></li>
                <li><a href="#dfd-level-1">DFD Level 1</a></li>
                <li><a href="#erd">Entity Relationship</a></li>
                <li><a href="#flowcharts">System Flowcharts</a></li>
                <li><a href="#data-dictionary">Data Dictionary</a></li>
                <li><a href="#constraints">System Constraints</a></li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="content">
            <!-- Table of Contents -->
            <div class="toc">
                <h3>📋 Table of Contents</h3>
                <ul>
                    <li><a href="#executive-summary">1. Executive Summary</a></li>
                    <li><a href="#context-diagram">2. Context Diagram</a>
                        <ul class="toc-level-2">
                            <li><a href="#external-entities">2.1 External Entities and Data Flows</a></li>
                            <li><a href="#system-boundaries">2.2 System Boundaries</a></li>
                        </ul>
                    </li>
                    <li><a href="#dfd-level-0">3. Data Flow Diagram Level 0</a>
                        <ul class="toc-level-2">
                            <li><a href="#major-processes">3.1 Major Processes</a></li>
                            <li><a href="#data-stores">3.2 Data Stores</a></li>
                        </ul>
                    </li>
                    <li><a href="#dfd-level-1">4. Data Flow Diagram Level 1</a>
                        <ul class="toc-level-2">
                            <li><a href="#authentication">4.1 User Authentication</a></li>
                            <li><a href="#registration">4.2 Parent Registration</a></li>
                            <li><a href="#student-mgmt">4.3 Student Management</a></li>
                            <li><a href="#assessment-mgmt">4.4 Assessment Management</a></li>
                        </ul>
                    </li>
                    <li><a href="#erd">5. Entity Relationship Diagram</a>
                        <ul class="toc-level-2">
                            <li><a href="#core-entities">5.1 Core Entities</a></li>
                            <li><a href="#relationships">5.2 Relationships and Cardinalities</a></li>
                        </ul>
                    </li>
                    <li><a href="#flowcharts">6. System Flowcharts</a>
                        <ul class="toc-level-2">
                            <li><a href="#login-process">6.1 User Login Process</a></li>
                            <li><a href="#registration-process">6.2 Registration Process</a></li>
                            <li><a href="#assessment-workflow">6.3 Assessment Workflow</a></li>
                        </ul>
                    </li>
                    <li><a href="#data-dictionary">7. Data Dictionary</a>
                        <ul class="toc-level-2">
                            <li><a href="#table-structures">7.1 Table Structures</a></li>
                            <li><a href="#grading-system">7.2 Malaysian Grading System</a></li>
                            <li><a href="#security-specs">7.3 Security Specifications</a></li>
                        </ul>
                    </li>
                    <li><a href="#constraints">8. System Constraints</a></li>
                </ul>
            </div>

            <!-- Executive Summary -->
            <section id="executive-summary" class="section">
                <h1>📊 Executive Summary</h1>

                <div class="info-box">
                    <strong>🎯 Project Overview:</strong> The SMKTMI (Sekolah Menengah Kebangsaan Tunku Mahmood Iskandar) School Management System is a comprehensive web-based application designed to streamline school operations and enhance communication between administrators, teachers, parents, and students.
                </div>

                <h2>🔑 Key System Features</h2>
                <ul>
                    <li><strong>Multi-Role Authentication System</strong> with role-based access control and security policies</li>
                    <li><strong>Student Information Management</strong> with parent-child relationships and academic tracking</li>
                    <li><strong>Assessment and Grading System</strong> using Malaysian education standards (A+ to G grading)</li>
                    <li><strong>Attendance Tracking</strong> with real-time reporting and summary statistics</li>
                    <li><strong>Discipline Management</strong> with automated notifications to students and parents</li>
                    <li><strong>Schedule Management</strong> with printing capabilities and timetable organization</li>
                    <li><strong>PIBG Payment Tracking</strong> for school fees and payment status monitoring</li>
                    <li><strong>Comprehensive Audit Logging</strong> for system activities and administrative actions</li>
                </ul>

                <h2>👥 System Users and Access Levels</h2>
                <table>
                    <thead>
                        <tr>
                            <th>User Role</th>
                            <th>Primary Functions</th>
                            <th>Access Level</th>
                            <th>Key Permissions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Admin</strong></td>
                            <td>System management, user creation, configuration</td>
                            <td>Full Access</td>
                            <td>Create/Edit/Delete all records, System configuration, Audit logs</td>
                        </tr>
                        <tr>
                            <td><strong>Teacher</strong></td>
                            <td>Student management, assessment marking, discipline reports</td>
                            <td>Functional Access</td>
                            <td>Mark assessments, Record attendance, Create discipline reports</td>
                        </tr>
                        <tr>
                            <td><strong>Parent</strong></td>
                            <td>Child registration, progress monitoring</td>
                            <td>View/Limited Edit</td>
                            <td>Register children, View academic progress, Update contact info</td>
                        </tr>
                        <tr>
                            <td><strong>Student</strong></td>
                            <td>Academic records viewing, schedule access</td>
                            <td>View Only</td>
                            <td>View grades, attendance, schedule, discipline records</td>
                        </tr>
                    </tbody>
                </table>

                <h2>🏗️ System Architecture</h2>
                <div class="success-box">
                    <strong>Three-Tier Architecture:</strong>
                    <ol>
                        <li><strong>Presentation Layer:</strong> Web-based user interface with role-specific dashboards</li>
                        <li><strong>Business Logic Layer:</strong> PHP-based application logic with security controls</li>
                        <li><strong>Data Layer:</strong> MySQL database with normalized table structure</li>
                    </ol>
                </div>

                <h2>💻 Technology Stack</h2>
                <table>
                    <thead>
                        <tr>
                            <th>Component</th>
                            <th>Technology</th>
                            <th>Version</th>
                            <th>Purpose</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Frontend</td>
                            <td>HTML5, CSS3, JavaScript, Bootstrap</td>
                            <td>5.x</td>
                            <td>User interface and responsive design</td>
                        </tr>
                        <tr>
                            <td>Backend</td>
                            <td>PHP</td>
                            <td>7.4+</td>
                            <td>Server-side logic and processing</td>
                        </tr>
                        <tr>
                            <td>Database</td>
                            <td>MySQL</td>
                            <td>5.7+</td>
                            <td>Data storage and management</td>
                        </tr>
                        <tr>
                            <td>Security</td>
                            <td>reCAPTCHA, Password Hashing</td>
                            <td>Latest</td>
                            <td>Authentication and security</td>
                        </tr>
                        <tr>
                            <td>Email</td>
                            <td>PHPMailer</td>
                            <td>6.x</td>
                            <td>Email verification and notifications</td>
                        </tr>
                    </tbody>
                </table>
            </section>

            <!-- Context Diagram -->
            <section id="context-diagram" class="section">
                <h1>🌐 Context Diagram</h1>

                <p>The context diagram shows the SMKTMI system as a single process interacting with four external entities. Each entity has specific data flows representing their interactions with the system.</p>

                <div class="diagram">
                    ┌─────────────────────────────────────────┐
                    │                                         │
                    │            SMKTMI SYSTEM                │
                    │         School Management               │
                    │                                         │
                    └─────────────────────────────────────────┘
                              ▲         ▲         ▲         ▲
                              │         │         │         │
                    ┌─────────┴─┐   ┌───┴───┐ ┌───┴───┐ ┌───┴─────┐
                    │   ADMIN   │   │TEACHER│ │PARENT │ │ STUDENT │
                    │           │   │       │ │       │ │         │
                    └───────────┘   └───────┘ └───────┘ └─────────┘
                </div>

                <h2 id="external-entities">🔄 External Entities and Data Flows</h2>

                <h3>👨‍💼 Admin (System Administrator)</h3>
                <div class="info-box">
                    <strong>Data Flows TO System:</strong>
                    <ul>
                        <li>User management (create, update, delete teachers/students)</li>
                        <li>Class and subject management</li>
                        <li>Assessment type creation</li>
                        <li>Schedule management</li>
                        <li>System configuration and settings</li>
                    </ul>
                    <strong>Data Flows FROM System:</strong>
                    <ul>
                        <li>System reports and statistics</li>
                        <li>Activity logs and audit trails</li>
                        <li>User lists and management interfaces</li>
                        <li>System status and performance metrics</li>
                    </ul>
                </div>

                <h3>👩‍🏫 Teacher (Subject Teacher)</h3>
                <div class="info-box">
                    <strong>Data Flows TO System:</strong>
                    <ul>
                        <li>Student attendance recording</li>
                        <li>Assessment marking and grading</li>
                        <li>Discipline report creation</li>
                        <li>Student information updates</li>
                        <li>PIBG payment status updates</li>
                    </ul>
                    <strong>Data Flows FROM System:</strong>
                    <ul>
                        <li>Student lists and class rosters</li>
                        <li>Assessment forms and marking interfaces</li>
                        <li>Schedule views and timetables</li>
                        <li>Attendance reports and summaries</li>
                    </ul>
                </div>

                <h3>👨‍👩‍👧‍👦 Parent (Student Guardian)</h3>
                <div class="info-box">
                    <strong>Data Flows TO System:</strong>
                    <ul>
                        <li>Child registration and enrollment</li>
                        <li>Profile updates and contact information</li>
                        <li>Information viewing requests</li>
                    </ul>
                    <strong>Data Flows FROM System:</strong>
                    <ul>
                        <li>Child academic progress reports</li>
                        <li>Attendance summaries and records</li>
                        <li>Discipline incident notifications</li>
                        <li>PIBG payment status</li>
                        <li>School announcements and communications</li>
                    </ul>
                </div>

                <h3>🎓 Student (School Student)</h3>
                <div class="info-box">
                    <strong>Data Flows TO System:</strong>
                    <ul>
                        <li>Profile viewing requests</li>
                        <li>Academic record access</li>
                        <li>Schedule information requests</li>
                        <li>Password change requests</li>
                    </ul>
                    <strong>Data Flows FROM System:</strong>
                    <ul>
                        <li>Academic results and grades</li>
                        <li>Attendance records and summaries</li>
                        <li>Class schedule information</li>
                        <li>Discipline records (if any)</li>
                        <li>Personal academic dashboard</li>
                    </ul>
                </div>

                <h2 id="system-boundaries">🔒 System Boundaries</h2>
                <div class="warning-box">
                    <strong>The SMKTMI system boundary encompasses:</strong>
                    <ul>
                        <li><strong>Internal Components:</strong> User authentication, data management, business logic</li>
                        <li><strong>External Interfaces:</strong> Email services, reCAPTCHA verification</li>
                        <li><strong>Data Storage:</strong> MySQL database with all school-related information</li>
                        <li><strong>Security Controls:</strong> Password policies, account lockout, audit logging</li>
                    </ul>
                </div>
            </section>

            <!-- DFD Level 0 -->
            <section id="dfd-level-0" class="section">
                <h1>📊 Data Flow Diagram Level 0 (System Overview)</h1>

                <p>The Level 0 DFD decomposes the SMKTMI system into major functional processes, showing how data flows between external entities, processes, and data stores.</p>

                <div class="diagram">
External Entities:    Processes:                    Data Stores:
┌─────────┐          ┌─────────────────┐           ┌─────────────────┐
│  ADMIN  │          │  1.0 User       │           │  D1: Users      │
└─────────┘          │  Authentication │           └─────────────────┘
                     └─────────────────┘
┌─────────┐          ┌─────────────────┐           ┌─────────────────┐
│ TEACHER │          │  2.0 Student    │           │  D2: Students   │
└─────────┘          │  Management     │           └─────────────────┘
                     └─────────────────┘
┌─────────┐          ┌─────────────────┐           ┌─────────────────┐
│ PARENT  │          │  3.0 Teacher    │           │  D3: Teachers   │
└─────────┘          │  Management     │           └─────────────────┘
                     └─────────────────┘
┌─────────┐          ┌─────────────────┐           ┌─────────────────┐
│ STUDENT │          │  4.0 Assessment │           │  D4: Parents    │
└─────────┘          │  Management     │           └─────────────────┘
                     └─────────────────┘
                     ┌─────────────────┐           ┌─────────────────┐
                     │  5.0 Attendance │           │  D5: Classrooms │
                     │  Management     │           └─────────────────┘
                     └─────────────────┘
                     ┌─────────────────┐           ┌─────────────────┐
                     │  6.0 Discipline │           │  D6: Subjects   │
                     │  Management     │           └─────────────────┘
                     └─────────────────┘
                     ┌─────────────────┐           ┌─────────────────┐
                     │  7.0 Schedule   │           │  D7: Assessment │
                     │  Management     │           └─────────────────┘
                     └─────────────────┘
                     ┌─────────────────┐           ┌─────────────────┐
                     │  8.0 PIBG       │           │  D8: Assessment │
                     │  Management     │           │      Results    │
                     └─────────────────┘           └─────────────────┘
                     ┌─────────────────┐           ┌─────────────────┐
                     │  9.0 Admin      │           │  D9: Attendance │
                     │  Logging        │           └─────────────────┘
                     └─────────────────┘
                                                   ┌─────────────────┐
                                                   │ D10: Discipline │
                                                   │     Incidents   │
                                                   └─────────────────┘

                                                   ┌─────────────────┐
                                                   │ D11: Schedules  │
                                                   └─────────────────┘

                                                   ┌─────────────────┐
                                                   │ D12: Admin Logs │
                                                   └─────────────────┘
                </div>

                <h2 id="major-processes">⚙️ Major Processes</h2>

                <table>
                    <thead>
                        <tr>
                            <th>Process</th>
                            <th>Function</th>
                            <th>Key Inputs</th>
                            <th>Key Outputs</th>
                            <th>Data Stores</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>1.0 User Authentication</strong></td>
                            <td>Validates user credentials and manages login security</td>
                            <td>Login credentials from all user types</td>
                            <td>Authentication results, role-based redirection</td>
                            <td>D1: Users</td>
                        </tr>
                        <tr>
                            <td><strong>2.0 Student Management</strong></td>
                            <td>Manages student enrollment, profiles, and academic records</td>
                            <td>Student data from Admin/Parent</td>
                            <td>Student records, class assignments</td>
                            <td>D2: Students, D5: Classrooms</td>
                        </tr>
                        <tr>
                            <td><strong>3.0 Teacher Management</strong></td>
                            <td>Manages teacher profiles and subject assignments</td>
                            <td>Teacher data from Admin</td>
                            <td>Teacher records, assignment confirmations</td>
                            <td>D3: Teachers, D6: Subjects</td>
                        </tr>
                        <tr>
                            <td><strong>4.0 Assessment Management</strong></td>
                            <td>Creates assessments and processes student results</td>
                            <td>Assessment types from Admin, marks from Teachers</td>
                            <td>Assessment records, grade calculations</td>
                            <td>D7: Assessment, D8: Assessment Results</td>
                        </tr>
                        <tr>
                            <td><strong>5.0 Attendance Management</strong></td>
                            <td>Records and tracks student attendance</td>
                            <td>Attendance data from Teachers</td>
                            <td>Attendance records, summary reports</td>
                            <td>D9: Attendance</td>
                        </tr>
                        <tr>
                            <td><strong>6.0 Discipline Management</strong></td>
                            <td>Manages disciplinary incidents and reports</td>
                            <td>Incident reports from Teachers</td>
                            <td>Discipline records, notifications</td>
                            <td>D10: Discipline Incidents</td>
                        </tr>
                        <tr>
                            <td><strong>7.0 Schedule Management</strong></td>
                            <td>Creates and manages class timetables</td>
                            <td>Schedule data from Admin</td>
                            <td>Timetables, schedule assignments</td>
                            <td>D11: Schedules</td>
                        </tr>
                        <tr>
                            <td><strong>8.0 PIBG Management</strong></td>
                            <td>Tracks PIBG payment status</td>
                            <td>Payment updates from Teachers</td>
                            <td>Payment status, reports</td>
                            <td>D2: Students (pibg_paid field)</td>
                        </tr>
                        <tr>
                            <td><strong>9.0 Admin Logging</strong></td>
                            <td>Records system activities for audit purposes</td>
                            <td>Activity data from all processes</td>
                            <td>Audit logs, activity reports</td>
                            <td>D12: Admin Logs</td>
                        </tr>
                    </tbody>
                </table>

                <h2 id="data-stores">🗄️ Data Stores</h2>

                <table>
                    <thead>
                        <tr>
                            <th>Store ID</th>
                            <th>Name</th>
                            <th>Description</th>
                            <th>Primary Key</th>
                            <th>Key Relationships</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>D1</td>
                            <td>Users</td>
                            <td>Central authentication table</td>
                            <td>user_id</td>
                            <td>Links to all role tables</td>
                        </tr>
                        <tr>
                            <td>D2</td>
                            <td>Students</td>
                            <td>Student profiles and academic data</td>
                            <td>student_id</td>
                            <td>Links to Parents, Classrooms</td>
                        </tr>
                        <tr>
                            <td>D3</td>
                            <td>Teachers</td>
                            <td>Teacher profiles and contact information</td>
                            <td>teacher_id</td>
                            <td>Links to Subjects, Classrooms</td>
                        </tr>
                        <tr>
                            <td>D4</td>
                            <td>Parents</td>
                            <td>Parent profiles and verification status</td>
                            <td>parent_id</td>
                            <td>Links to Students</td>
                        </tr>
                        <tr>
                            <td>D5</td>
                            <td>Classrooms</td>
                            <td>Class organization and assignments</td>
                            <td>classroom_id</td>
                            <td>Links to Teachers, Students</td>
                        </tr>
                        <tr>
                            <td>D6</td>
                            <td>Subjects</td>
                            <td>Academic subject definitions</td>
                            <td>subject_id</td>
                            <td>Links to Teachers, Assessments</td>
                        </tr>
                        <tr>
                            <td>D7</td>
                            <td>Assessment</td>
                            <td>Assessment types and schedules</td>
                            <td>assessment_id</td>
                            <td>Links to Subjects, Classrooms</td>
                        </tr>
                        <tr>
                            <td>D8</td>
                            <td>Assessment Results</td>
                            <td>Student marks and grades</td>
                            <td>result_id</td>
                            <td>Links to Students, Assessments</td>
                        </tr>
                        <tr>
                            <td>D9</td>
                            <td>Attendance</td>
                            <td>Daily attendance records</td>
                            <td>attendance_id</td>
                            <td>Links to Students, Teachers</td>
                        </tr>
                        <tr>
                            <td>D10</td>
                            <td>Discipline Incidents</td>
                            <td>Disciplinary reports and actions</td>
                            <td>incident_id</td>
                            <td>Links to Students, Teachers</td>
                        </tr>
                        <tr>
                            <td>D11</td>
                            <td>Schedules</td>
                            <td>Class timetables and assignments</td>
                            <td>schedule_id</td>
                            <td>Links to Teachers, Subjects, Classrooms</td>
                        </tr>
                        <tr>
                            <td>D12</td>
                            <td>Admin Logs</td>
                            <td>System activity audit trail</td>
                            <td>log_id</td>
                            <td>Links to Admins</td>
                        </tr>
                    </tbody>
                </table>

                <div class="success-box">
                    <strong>🔄 Major Data Flows:</strong>
                    <ol>
                        <li><strong>Authentication Flow:</strong> All users → Process 1.0 → User validation → Role-based redirection</li>
                        <li><strong>Student Data Flow:</strong> Admin/Parent → Process 2.0 → Student records → D2: Students</li>
                        <li><strong>Assessment Flow:</strong> Admin → Process 4.0 → Teacher → Assessment marking → D7, D8</li>
                        <li><strong>Discipline Flow:</strong> Teacher → Process 6.0 → Discipline records → D10 → Student/Parent view</li>
                        <li><strong>Attendance Flow:</strong> Teacher → Process 5.0 → Attendance records → D9 → Student/Parent view</li>
                    </ol>
                </div>
            </section>

            <!-- DFD Level 1 -->
            <section id="dfd-level-1" class="section">
                <h1>🔍 Data Flow Diagram Level 1 - Detailed Process Breakdown</h1>

                <h2 id="authentication">🔐 Process 1.0: User Authentication & Login Management</h2>

                <div class="diagram">
┌─────────┐    login_credentials    ┌─────────────────┐    user_data    ┌─────────────┐
│  USER   │ ────────────────────→   │  1.1 Validate  │ ──────────────→ │ D1: Users   │
└─────────┘                         │  Credentials    │                 └─────────────┘
     ▲                              └─────────────────┘                        │
     │                                       │                                 │
     │ authentication_result                 │ failed_attempts                 │
     │                                       ▼                                 │
     │                              ┌─────────────────┐                        │
     └──────────────────────────────│  1.2 Handle     │◄───────────────────────┘
                                    │  Login Security │
                                    └─────────────────┘
                                             │
                                             ▼
                                    ┌─────────────────┐    role_info    ┌─────────────┐
                                    │  1.3 Role-Based │ ──────────────→ │ D2-D4: Role │
                                    │  Redirection    │                 │ Tables      │
                                    └─────────────────┘                 └─────────────┘
                </div>

                <h3>Sub-Process Details:</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Sub-Process</th>
                            <th>Function</th>
                            <th>Key Activities</th>
                            <th>Business Rules</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>1.1 Validate Credentials</strong></td>
                            <td>Verifies user email and password against database records</td>
                            <td>
                                • Validate reCAPTCHA response<br>
                                • Check email existence in users table<br>
                                • Verify password hash using PHP password_verify()<br>
                                • Check account lockout status
                            </td>
                            <td>
                                • Email must exist in system<br>
                                • Password must match stored hash<br>
                                • Account must not be locked
                            </td>
                        </tr>
                        <tr>
                            <td><strong>1.2 Handle Login Security</strong></td>
                            <td>Manages failed login attempts and account lockout</td>
                            <td>
                                • Increment failed_attempts counter<br>
                                • Check if attempts >= 3<br>
                                • Set lockout_time if threshold reached<br>
                                • Calculate 2-minute cooldown period
                            </td>
                            <td>
                                • Account locks after 3 failed attempts<br>
                                • Lockout duration is 2 minutes<br>
                                • Counter resets on successful login
                            </td>
                        </tr>
                        <tr>
                            <td><strong>1.3 Role-Based Redirection</strong></td>
                            <td>Determines user dashboard based on role_id</td>
                            <td>
                                • Query role-specific table<br>
                                • Set session variables<br>
                                • Determine appropriate dashboard<br>
                                • Check additional permissions
                            </td>
                            <td>
                                • role_id 1 = Admin Dashboard<br>
                                • role_id 2 = Parent Dashboard (if verified)<br>
                                • role_id 3 = Teacher Dashboard<br>
                                • role_id 4 = Student Dashboard
                            </td>
                        </tr>
                    </tbody>
                </table>

                <h2 id="registration">📝 Process 2.0: Parent Registration & Verification</h2>

                <div class="diagram">
┌─────────┐  registration_data  ┌─────────────────┐  parent_record  ┌─────────────┐
│ PARENT  │ ──────────────────→ │  2.1 Validate   │ ──────────────→ │ D4: Parents │
└─────────┘                     │  Registration   │                 └─────────────┘
     ▲                          └─────────────────┘                        │
     │                                   │                                 │
     │ verification_email                │ user_account                    │
     │                                   ▼                                 │
     │                          ┌─────────────────┐                        │
     └──────────────────────────│  2.2 Create     │◄───────────────────────┘
                                │  User Account   │
                                └─────────────────┘
                                         │
                                         ▼
                                ┌─────────────────┐  verification_token  ┌─────────────┐
                                │  2.3 Send Email │ ──────────────────→  │ Email System│
                                │  Verification   │                      └─────────────┘
                                └─────────────────┘
                </div>

                <h3>Sub-Process Details:</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Sub-Process</th>
                            <th>Function</th>
                            <th>Key Activities</th>
                            <th>Business Rules</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>2.1 Validate Registration</strong></td>
                            <td>Validates parent registration form and enforces business rules</td>
                            <td>
                                • Validate email format and uniqueness<br>
                                • Enforce password policy<br>
                                • Check required field completion<br>
                                • Validate phone number format
                            </td>
                            <td>
                                • Email must be unique across all users<br>
                                • Password: 8+ chars, mixed case, digits, symbols<br>
                                • All required fields must be completed
                            </td>
                        </tr>
                        <tr>
                            <td><strong>2.2 Create User Account</strong></td>
                            <td>Creates user account and parent profile records</td>
                            <td>
                                • Insert record into users table<br>
                                • Generate user_id<br>
                                • Hash password using PHP password_hash()<br>
                                • Insert parent profile record
                            </td>
                            <td>
                                • role_id must be set to 2 (Parent)<br>
                                • Password must be securely hashed<br>
                                • Parent profile linked to user account
                            </td>
                        </tr>
                        <tr>
                            <td><strong>2.3 Send Email Verification</strong></td>
                            <td>Sends verification email with secure token</td>
                            <td>
                                • Generate secure verification URL<br>
                                • Compose verification email<br>
                                • Send email using PHPMailer<br>
                                • Set token expiration time
                            </td>
                            <td>
                                • Token must be cryptographically secure<br>
                                • Email must be sent successfully<br>
                                • Token expires after set duration
                            </td>
                        </tr>
                    </tbody>
                </table>

                <h2 id="student-mgmt">🎓 Process 3.0: Student Management (Admin Functions)</h2>

                <div class="diagram">
┌─────────┐  student_data  ┌─────────────────┐  validated_data  ┌─────────────┐
│  ADMIN  │ ─────────────→ │  3.1 Validate   │ ───────────────→ │ D2: Students│
└─────────┘                │  Student Data   │                  └─────────────┘
     ▲                     └─────────────────┘                         │
     │                              │                                  │
     │ student_list                 │ user_account                     │
     │                              ▼                                  │
     │                     ┌─────────────────┐                         │
     └─────────────────────│  3.2 Create     │◄────────────────────────┘
                           │  User Account   │
                           └─────────────────┘
                                    │
                                    ▼
                           ┌─────────────────┐  class_assignment  ┌─────────────┐
                           │  3.3 Assign     │ ─────────────────→ │D5:Classrooms│
                           │  to Class       │                    └─────────────┘
                           └─────────────────┘
                </div>

                <h3>Sub-Process Details:</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Sub-Process</th>
                            <th>Function</th>
                            <th>Key Activities</th>
                            <th>Business Rules</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>3.1 Validate Student Data</strong></td>
                            <td>Validates student information and checks for duplicates</td>
                            <td>
                                • Validate IC number uniqueness<br>
                                • Check parent_id existence<br>
                                • Verify classroom_id validity<br>
                                • Calculate age from birth date
                            </td>
                            <td>
                                • IC number must be unique across all students<br>
                                • Parent must exist in parents table<br>
                                • Classroom must exist and have capacity
                            </td>
                        </tr>
                        <tr>
                            <td><strong>3.2 Create User Account</strong></td>
                            <td>Creates user account for student with role_id = 4</td>
                            <td>
                                • Generate unique email if not provided<br>
                                • Create user account with role_id = 4<br>
                                • Hash password using security standards<br>
                                • Link user_id to student record
                            </td>
                            <td>
                                • role_id must be 4 (Student)<br>
                                • Email must be unique<br>
                                • Password must meet policy requirements
                            </td>
                        </tr>
                        <tr>
                            <td><strong>3.3 Assign to Class</strong></td>
                            <td>Assigns student to classroom and updates enrollment</td>
                            <td>
                                • Update student record with classroom_id<br>
                                • Check classroom capacity<br>
                                • Update class enrollment count<br>
                                • Set PIBG payment status to unpaid
                            </td>
                            <td>
                                • Student must be assigned to valid classroom<br>
                                • Classroom capacity must not be exceeded<br>
                                • PIBG status defaults to unpaid (0)
                            </td>
                        </tr>
                    </tbody>
                </table>

                <h2 id="assessment-mgmt">📊 Process 4.0: Assessment Management</h2>

                <div class="diagram">
┌─────────┐  assessment_type  ┌─────────────────┐  assessment_record  ┌─────────────┐
│  ADMIN  │ ────────────────→ │  4.1 Create     │ ──────────────────→ │D7:Assessment│
└─────────┘                   │  Assessment     │                     └─────────────┘
                              └─────────────────┘                            │
┌─────────┐  marks_data              │                                       │
│ TEACHER │ ──────────────────────────┼───────────────────────────────────────┘
└─────────┘                          ▼
     ▲                      ┌─────────────────┐  student_marks  ┌─────────────┐
     │                      │  4.2 Mark       │ ──────────────→ │D8:Assessment│
     │ marked_assessments   │  Assessment     │                 │   Results   │
     │                      └─────────────────┘                 └─────────────┘
     │                               │                                 │
     │                               │ grade_calculation               │
     │                               ▼                                 │
     │                      ┌─────────────────┐                        │
     └──────────────────────│  4.3 Calculate  │◄───────────────────────┘
                            │  Grades         │
                            └─────────────────┘
                </div>

                <div class="info-box">
                    <strong>Assessment Workflow:</strong>
                    <ol>
                        <li><strong>Admin creates assessment types</strong> for specific subjects and classes</li>
                        <li><strong>Teachers input marks</strong> for students in their assigned subjects</li>
                        <li><strong>System calculates grades</strong> using Malaysian grading system (A+ to G)</li>
                        <li><strong>Results are automatically available</strong> to students and parents</li>
                    </ol>
                </div>
            </section>

            <!-- ERD Section -->
            <section id="erd" class="section">
                <h1>🗂️ Entity Relationship Diagram (ERD)</h1>

                <h2>📊 Database Schema Overview</h2>
                <p>The SMKTMI system uses a normalized MySQL database with 14 core tables designed to support all school management functions while maintaining data integrity and performance.</p>

                <div class="diagram">
                    ┌─────────────────┐
                    │      USERS      │
                    │ PK: user_id     │
                    │     email       │
                    │ password_hash   │
                    │   role_id       │
                    │ failed_attempts │
                    │  lockout_time   │
                    │  created_at     │
                    └─────────────────┘
                             │
                    ┌────────┼────────┐
                    │        │        │
                    ▼        ▼        ▼
        ┌─────────────────┐ │ ┌─────────────────┐
        │     ADMINS      │ │ │    TEACHERS     │
        │ PK: admin_id    │ │ │ PK: teacher_id  │
        │ FK: user_id     │ │ │ FK: user_id     │
        │   full_name     │ │ │   full_name     │
        │ phone_number    │ │ │ phone_number    │
        │   last_login    │ │ │   last_login    │
        │  last_logout    │ │ │  last_logout    │
        │  created_at     │ │ │  created_at     │
        └─────────────────┘ │ └─────────────────┘
                            │          │
                            ▼          │
                ┌─────────────────┐    │
                │    PARENTS      │    │
                │ PK: parent_id   │    │
                │ FK: user_id     │    │
                │   full_name     │    │
                │ phone_number    │    │
                │    address      │    │
                │email_verified   │    │
                │verification_    │    │
                │    token        │    │
                │token_created_at │    │
                │  created_at     │    │
                └─────────────────┘    │
                         │             │
                         │             │
                         ▼             ▼
                ┌─────────────────┐ ┌─────────────────┐
                │    STUDENTS     │ │   CLASSROOMS    │
                │ PK: student_id  │ │ PK: classroom_id│
                │ FK: user_id     │ │ FK: teacher_id  │
                │ FK: parent_id   │ │  class_name     │
                │ FK: classroom_id│ │   tingkatan     │
                │   full_name     │ │  created_at     │
                │    no_ic        │ │  updated_at     │
                │    gender       │ └─────────────────┘
                │  birth_date     │          │
                │     age         │          │
                │   relation      │          │
                │  pibg_paid      │          │
                │  created_at     │          │
                └─────────────────┘          │
                         │                   │
                         │                   │
                         ▼                   ▼
                ┌─────────────────┐ ┌─────────────────┐
                │   SUBJECTS      │ │TEACHER_SUBJECT_ │
                │ PK: subject_id  │ │   CLASSROOMS    │
                │ subject_name    │ │ PK: tsc_id      │
                │  created_at     │ │ FK: teacher_id  │
                │  updated_at     │ │ FK: subject_id  │
                └─────────────────┘ │ FK: classroom_id│
                         │          │  created_at     │
                         │          │  updated_at     │
                         │          └─────────────────┘
                         │                   │
                         ▼                   │
                ┌─────────────────┐          │
                │   ASSESSMENT    │          │
                │ PK: assessment_ │          │
                │       id        │          │
                │ FK: subject_id  │          │
                │ FK: classroom_id│          │
                │assessment_type  │          │
                │assessment_date  │          │
                │  created_at     │          │
                │  updated_at     │          │
                └─────────────────┘          │
                         │                   │
                         ▼                   │
                ┌─────────────────┐          │
                │ ASSESSMENT_     │          │
                │    RESULT       │          │
                │ PK: result_id   │          │
                │ FK: student_id  │          │
                │ FK: assessment_ │          │
                │       id        │          │
                │     marks       │          │
                │  created_at     │          │
                │  updated_at     │          │
                └─────────────────┘          │
                                             │
                ┌─────────────────┐          │
                │   ATTENDANCE    │          │
                │ PK: attendance_ │          │
                │       id        │          │
                │ FK: student_id  │          │
                │ FK: teacher_id  │          │
                │ attendance_date │          │
                │    status       │          │
                │  created_at     │          │
                └─────────────────┘          │
                                             │
                ┌─────────────────┐          │
                │  DISCIPLINE_    │          │
                │   INCIDENTS     │          │
                │ PK: incident_id │          │
                │ FK: student_id  │          │
                │ FK: teacher_id  │          │
                │ incident_date   │          │
                │ incident_type   │          │
                │  description    │          │
                │ action_taken    │          │
                │   severity      │          │
                │    status       │          │
                │  created_at     │          │
                │  updated_at     │          │
                └─────────────────┘          │
                                             │
                ┌─────────────────┐          │
                │   SCHEDULES     │          │
                │ PK: schedule_id │          │
                │   day_name      │          │
                │  time_slot      │          │
                │ FK: teacher_id  │          │
                │ FK: subject_id  │          │
                │ FK: classroom_id│◄─────────┘
                │ academic_year   │
                │  created_at     │
                │  updated_at     │
                └─────────────────┘

                ┌─────────────────┐
                │   ADMIN_LOGS    │
                │ PK: log_id      │
                │ FK: admin_id    │
                │  admin_name     │
                │    action       │
                │ target_type     │
                │  target_id      │
                │ target_name     │
                │  description    │
                │  ip_address     │
                │  user_agent     │
                │  created_at     │
                └─────────────────┘
                </div>

                <h2 id="core-entities">🏗️ Core Entities</h2>

                <h3>👤 Primary Entities</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Entity</th>
                            <th>Purpose</th>
                            <th>Primary Key</th>
                            <th>Key Attributes</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>USERS</strong></td>
                            <td>Central authentication table for all system users</td>
                            <td>user_id</td>
                            <td>email (unique), password_hash, role_id, failed_attempts, lockout_time</td>
                        </tr>
                        <tr>
                            <td><strong>ADMINS</strong></td>
                            <td>Administrator profiles and system access</td>
                            <td>admin_id</td>
                            <td>user_id (FK), full_name, phone_number, last_login, last_logout</td>
                        </tr>
                        <tr>
                            <td><strong>TEACHERS</strong></td>
                            <td>Teacher profiles and contact information</td>
                            <td>teacher_id</td>
                            <td>user_id (FK), full_name, phone_number, last_login, last_logout</td>
                        </tr>
                        <tr>
                            <td><strong>PARENTS</strong></td>
                            <td>Parent profiles and verification status</td>
                            <td>parent_id</td>
                            <td>user_id (FK), full_name, phone_number, address, email_verified, verification_token</td>
                        </tr>
                        <tr>
                            <td><strong>STUDENTS</strong></td>
                            <td>Student profiles and academic information</td>
                            <td>student_id</td>
                            <td>user_id (FK), parent_id (FK), classroom_id (FK), full_name, no_ic (unique), gender, birth_date, age, relation, pibg_paid</td>
                        </tr>
                    </tbody>
                </table>

                <h3>🏫 Academic Structure Entities</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Entity</th>
                            <th>Purpose</th>
                            <th>Primary Key</th>
                            <th>Key Attributes</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>CLASSROOMS</strong></td>
                            <td>Class organization and teacher assignment</td>
                            <td>classroom_id</td>
                            <td>teacher_id (FK), class_name, tingkatan</td>
                        </tr>
                        <tr>
                            <td><strong>SUBJECTS</strong></td>
                            <td>Academic subject definitions</td>
                            <td>subject_id</td>
                            <td>subject_name</td>
                        </tr>
                        <tr>
                            <td><strong>TEACHER_SUBJECT_CLASSROOMS</strong></td>
                            <td>Resolves M:M:M relationships between teachers, subjects, and classrooms</td>
                            <td>tsc_id</td>
                            <td>teacher_id (FK), subject_id (FK), classroom_id (FK)</td>
                        </tr>
                    </tbody>
                </table>

                <h3>📊 Assessment and Performance Entities</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Entity</th>
                            <th>Purpose</th>
                            <th>Primary Key</th>
                            <th>Key Attributes</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>ASSESSMENT</strong></td>
                            <td>Assessment/examination definitions</td>
                            <td>assessment_id</td>
                            <td>subject_id (FK), classroom_id (FK), assessment_type, assessment_date</td>
                        </tr>
                        <tr>
                            <td><strong>ASSESSMENT_RESULT</strong></td>
                            <td>Student assessment marks and results</td>
                            <td>result_id</td>
                            <td>student_id (FK), assessment_id (FK), marks (DECIMAL 5,2)</td>
                        </tr>
                    </tbody>
                </table>

                <h3>📋 Operational Entities</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Entity</th>
                            <th>Purpose</th>
                            <th>Primary Key</th>
                            <th>Key Attributes</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>ATTENDANCE</strong></td>
                            <td>Daily student attendance tracking</td>
                            <td>attendance_id</td>
                            <td>student_id (FK), teacher_id (FK), attendance_date, status (Hadir/Tidak Hadir/Lewat)</td>
                        </tr>
                        <tr>
                            <td><strong>DISCIPLINE_INCIDENTS</strong></td>
                            <td>Student disciplinary records and tracking</td>
                            <td>incident_id</td>
                            <td>student_id (FK), teacher_id (FK), incident_date, incident_type, description, action_taken, severity, status</td>
                        </tr>
                        <tr>
                            <td><strong>SCHEDULES</strong></td>
                            <td>Class timetable and schedule management</td>
                            <td>schedule_id</td>
                            <td>teacher_id (FK), subject_id (FK), classroom_id (FK), day_name, time_slot, academic_year</td>
                        </tr>
                        <tr>
                            <td><strong>ADMIN_LOGS</strong></td>
                            <td>System activity audit trail</td>
                            <td>log_id</td>
                            <td>admin_id (FK), admin_name, action, target_type, target_id, target_name, description, ip_address, user_agent</td>
                        </tr>
                    </tbody>
                </table>

                <h2 id="relationships">🔗 Relationships and Cardinalities</h2>

                <table>
                    <thead>
                        <tr>
                            <th>Relationship</th>
                            <th>Cardinality</th>
                            <th>Description</th>
                            <th>Business Rule</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>USERS ↔ ADMINS</td>
                            <td>1:1</td>
                            <td>One user account per admin</td>
                            <td>Mandatory participation for admins</td>
                        </tr>
                        <tr>
                            <td>USERS ↔ TEACHERS</td>
                            <td>1:1</td>
                            <td>One user account per teacher</td>
                            <td>Mandatory participation for teachers</td>
                        </tr>
                        <tr>
                            <td>USERS ↔ PARENTS</td>
                            <td>1:1</td>
                            <td>One user account per parent</td>
                            <td>Mandatory participation for parents</td>
                        </tr>
                        <tr>
                            <td>USERS ↔ STUDENTS</td>
                            <td>1:1</td>
                            <td>One user account per student</td>
                            <td>Mandatory participation for students</td>
                        </tr>
                        <tr>
                            <td>PARENTS ↔ STUDENTS</td>
                            <td>1:M</td>
                            <td>One parent can have multiple children</td>
                            <td>Optional participation (parent may not have registered children yet)</td>
                        </tr>
                        <tr>
                            <td>TEACHERS ↔ CLASSROOMS</td>
                            <td>1:M</td>
                            <td>One teacher can be class teacher for multiple classes</td>
                            <td>Optional participation (teacher may not be a class teacher)</td>
                        </tr>
                        <tr>
                            <td>CLASSROOMS ↔ STUDENTS</td>
                            <td>1:M</td>
                            <td>One classroom contains multiple students</td>
                            <td>Mandatory participation for students</td>
                        </tr>
                        <tr>
                            <td>TEACHERS ↔ TSC</td>
                            <td>1:M</td>
                            <td>One teacher can have multiple subject assignments</td>
                            <td>Resolves M:M:M relationship</td>
                        </tr>
                        <tr>
                            <td>SUBJECTS ↔ ASSESSMENT</td>
                            <td>1:M</td>
                            <td>One subject can have multiple assessments</td>
                            <td>Mandatory participation for assessments</td>
                        </tr>
                        <tr>
                            <td>STUDENTS ↔ ASSESSMENT_RESULT</td>
                            <td>1:M</td>
                            <td>One student can have multiple assessment results</td>
                            <td>Optional participation</td>
                        </tr>
                        <tr>
                            <td>ASSESSMENT ↔ ASSESSMENT_RESULT</td>
                            <td>1:M</td>
                            <td>One assessment can have multiple student results</td>
                            <td>Optional participation</td>
                        </tr>
                    </tbody>
                </table>

                <div class="success-box">
                    <strong>🔑 Key Design Principles:</strong>
                    <ul>
                        <li><strong>Normalization:</strong> Database is normalized to 3NF to eliminate redundancy</li>
                        <li><strong>Referential Integrity:</strong> Foreign key constraints ensure data consistency</li>
                        <li><strong>Scalability:</strong> Design supports growth in users and data volume</li>
                        <li><strong>Security:</strong> Sensitive data is properly protected and access-controlled</li>
                        <li><strong>Audit Trail:</strong> All critical operations are logged for accountability</li>
                    </ul>
                </div>
            </section>

            <!-- System Flowcharts -->
            <section id="flowcharts" class="section">
                <h1>🔄 System Flowcharts</h1>

                <h2 id="login-process">🔐 User Login Process</h2>
                <p>This flowchart shows the complete user authentication process including security measures and role-based redirection.</p>

                <div class="diagram">
                    ┌─────────────┐
                    │    START    │
                    └─────────────┘
                           │
                           ▼
                    ┌─────────────┐
                    │ Enter Email │
                    │ & Password  │
                    └─────────────┘
                           │
                           ▼
                    ┌─────────────┐
                    │ Validate    │
                    │ reCAPTCHA   │
                    └─────────────┘
                           │
                           ▼
                    ┌─────────────┐      No     ┌─────────────┐
                    │ reCAPTCHA   │ ──────────→ │ Show Error  │
                    │ Valid?      │             │ Message     │
                    └─────────────┘             └─────────────┘
                           │ Yes                        │
                           ▼                           │
                    ┌─────────────┐                    │
                    │ Check User  │                    │
                    │ in Database │                    │
                    └─────────────┘                    │
                           │                           │
                           ▼                           │
                    ┌─────────────┐      No            │
                    │ User        │ ──────────────────┘
                    │ Exists?     │
                    └─────────────┘
                           │ Yes
                           ▼
                    ┌─────────────┐
                    │ Check       │
                    │ Account     │
                    │ Lockout     │
                    └─────────────┘
                           │
                           ▼
                    ┌─────────────┐      Yes    ┌─────────────┐
                    │ Account     │ ──────────→ │ Show        │
                    │ Locked?     │             │ Lockout Msg │
                    └─────────────┘             └─────────────┘
                           │ No                         │
                           ▼                           │
                    ┌─────────────┐                    │
                    │ Verify      │                    │
                    │ Password    │                    │
                    └─────────────┘                    │
                           │                           │
                           ▼                           │
                    ┌─────────────┐      No            │
                    │ Password    │ ──────────────────┘
                    │ Correct?    │
                    └─────────────┘
                           │ Yes
                           ▼
                    ┌─────────────┐
                    │ Reset Failed│
                    │ Attempts    │
                    └─────────────┘
                           │
                           ▼
                    ┌─────────────┐
                    │ Check User  │
                    │ Role        │
                    └─────────────┘
                           │
                    ┌──────┼──────┐
                    │      │      │
                    ▼      ▼      ▼
            ┌─────────┐ ┌─────────┐ ┌─────────┐
            │ Admin   │ │ Teacher │ │ Parent/ │
            │Dashboard│ │Dashboard│ │ Student │
            └─────────┘ └─────────┘ │Dashboard│
                                    └─────────┘
                </div>

                <div class="info-box">
                    <strong>🔒 Security Features:</strong>
                    <ul>
                        <li><strong>reCAPTCHA Verification:</strong> Prevents automated attacks</li>
                        <li><strong>Account Lockout:</strong> 3 failed attempts = 2-minute lockout</li>
                        <li><strong>Password Hashing:</strong> Secure password storage using PHP password_hash()</li>
                        <li><strong>Role-Based Access:</strong> Users redirected to appropriate dashboards</li>
                        <li><strong>Session Management:</strong> Secure session handling with timeout</li>
                    </ul>
                </div>

                <h2 id="registration-process">📝 Parent Registration & Email Verification Process</h2>
                <p>This flowchart details the parent registration process including email verification requirements.</p>

                <div class="diagram">
                    ┌─────────────┐
                    │    START    │
                    └─────────────┘
                           │
                           ▼
                    ┌─────────────┐
                    │ Fill        │
                    │ Registration│
                    │ Form        │
                    └─────────────┘
                           │
                           ▼
                    ┌─────────────┐
                    │ Validate    │
                    │ Password    │
                    │ Policy      │
                    └─────────────┘
                           │
                           ▼
                    ┌─────────────┐      No     ┌─────────────┐
                    │ Password    │ ──────────→ │ Show Policy │
                    │ Valid?      │             │ Error       │
                    └─────────────┘             └─────────────┘
                           │ Yes                        │
                           ▼                           │
                    ┌─────────────┐                    │
                    │ Check Email │                    │
                    │ Uniqueness  │                    │
                    └─────────────┘                    │
                           │                           │
                           ▼                           │
                    ┌─────────────┐      Yes           │
                    │ Email       │ ──────────────────┘
                    │ Exists?     │
                    └─────────────┘
                           │ No
                           ▼
                    ┌─────────────┐
                    │ Create User │
                    │ Account     │
                    │ (role_id=2) │
                    └─────────────┘
                           │
                           ▼
                    ┌─────────────┐
                    │ Create      │
                    │ Parent      │
                    │ Record      │
                    └─────────────┘
                           │
                           ▼
                    ┌─────────────┐
                    │ Generate    │
                    │ Verification│
                    │ Token       │
                    └─────────────┘
                           │
                           ▼
                    ┌─────────────┐
                    │ Send        │
                    │ Verification│
                    │ Email       │
                    └─────────────┘
                           │
                           ▼
                    ┌─────────────┐
                    │ Show        │
                    │ Success     │
                    │ Message     │
                    └─────────────┘
                           │
                           ▼
                    ┌─────────────┐
                    │ User Clicks │
                    │ Email Link  │
                    └─────────────┘
                           │
                           ▼
                    ┌─────────────┐
                    │ Validate    │
                    │ Token &     │
                    │ Expiry      │
                    └─────────────┘
                           │
                           ▼
                    ┌─────────────┐      No     ┌─────────────┐
                    │ Token       │ ──────────→ │ Show Error  │
                    │ Valid?      │             │ Message     │
                    └─────────────┘             └─────────────┘
                           │ Yes
                           ▼
                    ┌─────────────┐
                    │ Set         │
                    │ email_      │
                    │ verified=1  │
                    └─────────────┘
                           │
                           ▼
                    ┌─────────────┐
                    │ Redirect to │
                    │ Login Page  │
                    └─────────────┘
                </div>

                <h2 id="assessment-workflow">📊 Assessment Creation and Marking Workflow</h2>
                <p>This flowchart shows the complete assessment process from admin creation to student/parent viewing.</p>

                <div class="diagram">
                    ┌─────────────┐
                    │ ADMIN:      │
                    │ Create      │
                    │ Assessment  │
                    │ Type        │
                    └─────────────┘
                           │
                           ▼
                    ┌─────────────┐
                    │ Select      │
                    │ Class       │
                    └─────────────┘
                           │
                           ▼
                    ┌─────────────┐
                    │ Select      │
                    │ Subjects    │
                    │ for Class   │
                    └─────────────┘
                           │
                           ▼
                    ┌─────────────┐
                    │ Save        │
                    │ Assessment  │
                    │ Records     │
                    └─────────────┘
                           │
                           ▼
                    ┌─────────────┐
                    │ TEACHER:    │
                    │ View        │
                    │ Available   │
                    │ Assessments │
                    └─────────────┘
                           │
                           ▼
                    ┌─────────────┐
                    │ Select      │
                    │ Assessment  │
                    │ to Mark     │
                    └─────────────┘
                           │
                           ▼
                    ┌─────────────┐
                    │ View        │
                    │ Student     │
                    │ List        │
                    └─────────────┘
                           │
                           ▼
                    ┌─────────────┐
                    │ Enter       │
                    │ Marks for   │
                    │ Each Student│
                    └─────────────┘
                           │
                           ▼
                    ┌─────────────┐
                    │ Validate    │
                    │ Marks       │
                    │ (0-100)     │
                    └─────────────┘
                           │
                           ▼
                    ┌─────────────┐
                    │ Calculate   │
                    │ Grades      │
                    │ (A+ to G)   │
                    └─────────────┘
                           │
                           ▼
                    ┌─────────────┐
                    │ Save        │
                    │ Assessment  │
                    │ Results     │
                    └─────────────┘
                           │
                           ▼
                    ┌─────────────┐
                    │ STUDENT/    │
                    │ PARENT:     │
                    │ View        │
                    │ Results     │
                    └─────────────┘
                </div>

                <div class="warning-box">
                    <strong>⚠️ Assessment Business Rules:</strong>
                    <ul>
                        <li><strong>Admin Role:</strong> Only admins can create assessment types</li>
                        <li><strong>Teacher Assignment:</strong> Teachers can only mark subjects they're assigned to</li>
                        <li><strong>Mark Validation:</strong> Marks must be between 0.00 and 100.00</li>
                        <li><strong>Grade Calculation:</strong> Automatic using Malaysian grading system</li>
                        <li><strong>Immediate Availability:</strong> Results visible to students/parents immediately after marking</li>
                    </ul>
                </div>
            </section>

            <!-- Data Dictionary -->
            <section id="data-dictionary" class="section">
                <h1>📚 Data Dictionary</h1>

                <h2 id="table-structures">🗃️ Table Structures and Specifications</h2>

                <h3>👤 USERS Table (Central Authentication)</h3>
                <p><strong>Purpose:</strong> Central authentication and user management for all system users</p>

                <table>
                    <thead>
                        <tr>
                            <th>Column Name</th>
                            <th>Data Type</th>
                            <th>Constraints</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><code>user_id</code></td>
                            <td>INT(11)</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>Unique user identifier</td>
                        </tr>
                        <tr>
                            <td><code>email</code></td>
                            <td>VARCHAR(255)</td>
                            <td>UNIQUE, NOT NULL</td>
                            <td>User email address</td>
                        </tr>
                        <tr>
                            <td><code>password_hash</code></td>
                            <td>VARCHAR(255)</td>
                            <td>NOT NULL</td>
                            <td>Hashed password using PHP password_hash()</td>
                        </tr>
                        <tr>
                            <td><code>role_id</code></td>
                            <td>INT(11)</td>
                            <td>NOT NULL</td>
                            <td>Role identifier (1=Admin, 2=Parent, 3=Teacher, 4=Student)</td>
                        </tr>
                        <tr>
                            <td><code>failed_attempts</code></td>
                            <td>INT(11)</td>
                            <td>DEFAULT 0</td>
                            <td>Failed login attempt counter for security</td>
                        </tr>
                        <tr>
                            <td><code>lockout_time</code></td>
                            <td>DATETIME</td>
                            <td>NULL</td>
                            <td>Account lockout timestamp (2-minute duration)</td>
                        </tr>
                        <tr>
                            <td><code>created_at</code></td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>Record creation timestamp</td>
                        </tr>
                    </tbody>
                </table>

                <div class="info-box">
                    <strong>🔒 Business Rules:</strong>
                    <ul>
                        <li>Email must be unique across entire system</li>
                        <li>Password must meet policy: 8+ characters, uppercase, lowercase, digit, symbol</li>
                        <li>Account locks after 3 failed attempts for 2 minutes</li>
                        <li>Role_id determines dashboard redirection and access permissions</li>
                    </ul>
                </div>

                <h3>🎓 STUDENTS Table (Student Profiles)</h3>
                <p><strong>Purpose:</strong> Student profiles and academic information management</p>

                <table>
                    <thead>
                        <tr>
                            <th>Column Name</th>
                            <th>Data Type</th>
                            <th>Constraints</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><code>student_id</code></td>
                            <td>INT(11)</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>Unique student identifier</td>
                        </tr>
                        <tr>
                            <td><code>user_id</code></td>
                            <td>INT(11)</td>
                            <td>FOREIGN KEY → users.user_id, UNIQUE</td>
                            <td>Reference to user account</td>
                        </tr>
                        <tr>
                            <td><code>parent_id</code></td>
                            <td>INT(11)</td>
                            <td>FOREIGN KEY → parents.parent_id</td>
                            <td>Reference to parent/guardian</td>
                        </tr>
                        <tr>
                            <td><code>classroom_id</code></td>
                            <td>INT(11)</td>
                            <td>FOREIGN KEY → classrooms.classroom_id</td>
                            <td>Assigned classroom</td>
                        </tr>
                        <tr>
                            <td><code>full_name</code></td>
                            <td>VARCHAR(255)</td>
                            <td>NOT NULL</td>
                            <td>Student full name</td>
                        </tr>
                        <tr>
                            <td><code>no_ic</code></td>
                            <td>VARCHAR(20)</td>
                            <td>UNIQUE, NOT NULL</td>
                            <td>Malaysian Identity Card number</td>
                        </tr>
                        <tr>
                            <td><code>gender</code></td>
                            <td>ENUM('Lelaki','Perempuan')</td>
                            <td>NOT NULL</td>
                            <td>Student gender</td>
                        </tr>
                        <tr>
                            <td><code>birth_date</code></td>
                            <td>DATE</td>
                            <td>NOT NULL</td>
                            <td>Date of birth</td>
                        </tr>
                        <tr>
                            <td><code>age</code></td>
                            <td>INT(11)</td>
                            <td>NOT NULL</td>
                            <td>Current age (calculated from birth_date)</td>
                        </tr>
                        <tr>
                            <td><code>relation</code></td>
                            <td>VARCHAR(50)</td>
                            <td>NOT NULL</td>
                            <td>Relationship to parent (Anak, Anak Angkat, etc.)</td>
                        </tr>
                        <tr>
                            <td><code>pibg_paid</code></td>
                            <td>TINYINT(1)</td>
                            <td>DEFAULT 0</td>
                            <td>PIBG payment status (0=Unpaid, 1=Paid)</td>
                        </tr>
                        <tr>
                            <td><code>created_at</code></td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>Record creation timestamp</td>
                        </tr>
                    </tbody>
                </table>

                <h3>📊 ASSESSMENT_RESULT Table (Student Results)</h3>
                <p><strong>Purpose:</strong> Student assessment marks and calculated grades</p>

                <table>
                    <thead>
                        <tr>
                            <th>Column Name</th>
                            <th>Data Type</th>
                            <th>Constraints</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><code>result_id</code></td>
                            <td>INT(11)</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>Unique result identifier</td>
                        </tr>
                        <tr>
                            <td><code>student_id</code></td>
                            <td>INT(11)</td>
                            <td>FOREIGN KEY → students.student_id</td>
                            <td>Student being assessed</td>
                        </tr>
                        <tr>
                            <td><code>assessment_id</code></td>
                            <td>INT(11)</td>
                            <td>FOREIGN KEY → assessment.assessment_id</td>
                            <td>Assessment reference</td>
                        </tr>
                        <tr>
                            <td><code>marks</code></td>
                            <td>DECIMAL(5,2)</td>
                            <td>NOT NULL, DEFAULT 0.00</td>
                            <td>Marks obtained (0.00-100.00)</td>
                        </tr>
                        <tr>
                            <td><code>created_at</code></td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>Record creation timestamp</td>
                        </tr>
                        <tr>
                            <td><code>updated_at</code></td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>Last modification timestamp</td>
                        </tr>
                    </tbody>
                </table>

                <div class="warning-box">
                    <strong>⚠️ Business Rules:</strong>
                    <ul>
                        <li>Marks range from 0.00 to 100.00</li>
                        <li>Unique constraint on (student_id, assessment_id) - one result per student per assessment</li>
                        <li>Grades calculated automatically using Malaysian grading system</li>
                        <li>Only teachers assigned to the subject can enter/modify marks</li>
                    </ul>
                </div>

                <h3>🚨 DISCIPLINE_INCIDENTS Table (Disciplinary Records)</h3>
                <p><strong>Purpose:</strong> Student disciplinary records and incident tracking</p>

                <table>
                    <thead>
                        <tr>
                            <th>Column Name</th>
                            <th>Data Type</th>
                            <th>Constraints</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><code>incident_id</code></td>
                            <td>INT(11)</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>Unique incident identifier</td>
                        </tr>
                        <tr>
                            <td><code>student_id</code></td>
                            <td>INT(11)</td>
                            <td>FOREIGN KEY → students.student_id</td>
                            <td>Student involved in incident</td>
                        </tr>
                        <tr>
                            <td><code>teacher_id</code></td>
                            <td>INT(11)</td>
                            <td>FOREIGN KEY → teachers.teacher_id</td>
                            <td>Reporting teacher</td>
                        </tr>
                        <tr>
                            <td><code>incident_date</code></td>
                            <td>DATE</td>
                            <td>NOT NULL</td>
                            <td>Date when incident occurred</td>
                        </tr>
                        <tr>
                            <td><code>incident_type</code></td>
                            <td>VARCHAR(100)</td>
                            <td>NOT NULL</td>
                            <td>Type of disciplinary issue</td>
                        </tr>
                        <tr>
                            <td><code>description</code></td>
                            <td>TEXT</td>
                            <td>NOT NULL</td>
                            <td>Detailed incident description (min 10 characters)</td>
                        </tr>
                        <tr>
                            <td><code>action_taken</code></td>
                            <td>TEXT</td>
                            <td>NULL</td>
                            <td>Actions taken by teacher/school</td>
                        </tr>
                        <tr>
                            <td><code>severity</code></td>
                            <td>ENUM('Ringan','Sederhana','Berat')</td>
                            <td>DEFAULT 'Ringan'</td>
                            <td>Severity level</td>
                        </tr>
                        <tr>
                            <td><code>status</code></td>
                            <td>ENUM('Baru','Dalam Tindakan','Selesai')</td>
                            <td>DEFAULT 'Baru'</td>
                            <td>Current status</td>
                        </tr>
                    </tbody>
                </table>

                <div class="info-box">
                    <strong>📋 Incident Types:</strong>
                    <ul>
                        <li><strong>Ponteng Kelas</strong> (Skipping Class)</li>
                        <li><strong>Tidak Memakai Pakaian Seragam Lengkap</strong> (Incomplete Uniform)</li>
                        <li><strong>Berkelakuan Tidak Senonoh</strong> (Inappropriate Behavior)</li>
                        <li><strong>Meniru Semasa Peperiksaan</strong> (Cheating During Examination)</li>
                        <li><strong>Membawa Telefon Bimbit Tanpa Kebenaran</strong> (Unauthorized Mobile Phone)</li>
                        <li><strong>Merokok di Kawasan Sekolah</strong> (Smoking on School Premises)</li>
                        <li><strong>Bergaduh</strong> (Fighting)</li>
                        <li><strong>Vandalisme</strong> (Vandalism)</li>
                        <li><strong>Buli</strong> (Bullying)</li>
                    </ul>
                </div>

                <h2 id="grading-system">🎯 Malaysian Grading System</h2>
                <p>The SMKTMI system implements the standard Malaysian secondary school grading system with automatic grade calculation based on percentage marks.</p>

                <table>
                    <thead>
                        <tr>
                            <th>Grade</th>
                            <th>Percentage Range</th>
                            <th>Malay Description</th>
                            <th>English Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="background-color: #d5f4e6;">
                            <td><strong>A+</strong></td>
                            <td>90-100%</td>
                            <td>Cemerlang Tertinggi</td>
                            <td>Excellent Plus</td>
                        </tr>
                        <tr style="background-color: #d5f4e6;">
                            <td><strong>A</strong></td>
                            <td>80-89%</td>
                            <td>Cemerlang Tinggi</td>
                            <td>Excellent</td>
                        </tr>
                        <tr style="background-color: #d5f4e6;">
                            <td><strong>A-</strong></td>
                            <td>70-79%</td>
                            <td>Cemerlang</td>
                            <td>Excellent Minus</td>
                        </tr>
                        <tr style="background-color: #e8f4fd;">
                            <td><strong>B+</strong></td>
                            <td>65-69%</td>
                            <td>Kepujian Tertinggi</td>
                            <td>Credit Plus</td>
                        </tr>
                        <tr style="background-color: #e8f4fd;">
                            <td><strong>B</strong></td>
                            <td>60-64%</td>
                            <td>Kepujian Tinggi</td>
                            <td>Credit</td>
                        </tr>
                        <tr style="background-color: #e8f4fd;">
                            <td><strong>C+</strong></td>
                            <td>55-59%</td>
                            <td>Kepujian Atas</td>
                            <td>Credit Plus</td>
                        </tr>
                        <tr style="background-color: #e8f4fd;">
                            <td><strong>C</strong></td>
                            <td>50-54%</td>
                            <td>Kepujian</td>
                            <td>Credit</td>
                        </tr>
                        <tr style="background-color: #fef9e7;">
                            <td><strong>D</strong></td>
                            <td>45-49%</td>
                            <td>Lulus Atas</td>
                            <td>Pass Plus</td>
                        </tr>
                        <tr style="background-color: #fef9e7;">
                            <td><strong>E</strong></td>
                            <td>40-44%</td>
                            <td>Lulus</td>
                            <td>Pass</td>
                        </tr>
                        <tr style="background-color: #ffebee;">
                            <td><strong>G</strong></td>
                            <td>0-39%</td>
                            <td>Gagal</td>
                            <td>Fail</td>
                        </tr>
                    </tbody>
                </table>

                <div class="success-box">
                    <strong>📊 Grading Implementation:</strong>
                    <ul>
                        <li><strong>Storage:</strong> Marks stored as DECIMAL(5,2) allowing values from 0.00 to 100.00</li>
                        <li><strong>Calculation:</strong> Grades calculated in real-time using PHP conditional logic</li>
                        <li><strong>Display:</strong> Grades shown in black font without color coding</li>
                        <li><strong>Boundaries:</strong> Grade boundaries are fixed and cannot be modified by users</li>
                    </ul>
                </div>

                <h2 id="security-specs">🔐 Security Specifications</h2>

                <h3>🔑 Password Policy</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Requirement</th>
                            <th>Specification</th>
                            <th>Implementation</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Minimum Length</td>
                            <td>8 characters</td>
                            <td>Enforced during registration and password changes</td>
                        </tr>
                        <tr>
                            <td>Uppercase Letter</td>
                            <td>At least one (A-Z)</td>
                            <td>Real-time validation with JavaScript</td>
                        </tr>
                        <tr>
                            <td>Lowercase Letter</td>
                            <td>At least one (a-z)</td>
                            <td>Server-side validation with PHP</td>
                        </tr>
                        <tr>
                            <td>Digit</td>
                            <td>At least one (0-9)</td>
                            <td>Password hashing using PHP password_hash()</td>
                        </tr>
                        <tr>
                            <td>Special Character</td>
                            <td>At least one symbol</td>
                            <td>Regex: <code>/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$/</code></td>
                        </tr>
                    </tbody>
                </table>

                <h3>🛡️ Account Security</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Security Feature</th>
                            <th>Configuration</th>
                            <th>Purpose</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Account Lockout</td>
                            <td>3 failed attempts = 2-minute lockout</td>
                            <td>Prevent brute force attacks</td>
                        </tr>
                        <tr>
                            <td>reCAPTCHA</td>
                            <td>Required for all login attempts</td>
                            <td>Prevent automated attacks</td>
                        </tr>
                        <tr>
                            <td>Session Management</td>
                            <td>Role-based access control with timeout</td>
                            <td>Secure user sessions</td>
                        </tr>
                        <tr>
                            <td>Password Hashing</td>
                            <td>PHP password_hash() with salt</td>
                            <td>Secure password storage</td>
                        </tr>
                        <tr>
                            <td>Email Verification</td>
                            <td>Required for parent accounts</td>
                            <td>Verify email ownership</td>
                        </tr>
                    </tbody>
                </table>
            </section>

            <!-- System Constraints -->
            <section id="constraints" class="section">
                <h1>⚙️ System Constraints and Implementation Notes</h1>

                <h2>💻 Technical Requirements</h2>

                <h3>🖥️ Server Requirements</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Component</th>
                            <th>Minimum Requirement</th>
                            <th>Recommended</th>
                            <th>Purpose</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Web Server</td>
                            <td>Apache 2.4+ or Nginx 1.18+</td>
                            <td>Apache 2.4.50+ or Nginx 1.20+</td>
                            <td>HTTP server for web application</td>
                        </tr>
                        <tr>
                            <td>PHP Version</td>
                            <td>7.4</td>
                            <td>8.0+</td>
                            <td>Server-side scripting language</td>
                        </tr>
                        <tr>
                            <td>Database</td>
                            <td>MySQL 5.7+ or MariaDB 10.3+</td>
                            <td>MySQL 8.0+ or MariaDB 10.6+</td>
                            <td>Data storage and management</td>
                        </tr>
                        <tr>
                            <td>Memory</td>
                            <td>2GB RAM</td>
                            <td>4GB+ RAM</td>
                            <td>Application performance</td>
                        </tr>
                        <tr>
                            <td>Storage</td>
                            <td>10GB</td>
                            <td>50GB+</td>
                            <td>Application and database storage</td>
                        </tr>
                    </tbody>
                </table>

                <h3>🌐 Client Requirements</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Component</th>
                            <th>Requirement</th>
                            <th>Notes</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Browser</td>
                            <td>Modern web browser with JavaScript enabled</td>
                            <td>Chrome 90+, Firefox 88+, Safari 14+, Edge 90+</td>
                        </tr>
                        <tr>
                            <td>Internet Connection</td>
                            <td>Stable internet connection</td>
                            <td>Required for reCAPTCHA and email verification</td>
                        </tr>
                        <tr>
                            <td>Screen Resolution</td>
                            <td>Minimum 1024x768</td>
                            <td>Responsive design supports mobile devices</td>
                        </tr>
                        <tr>
                            <td>JavaScript</td>
                            <td>Enabled</td>
                            <td>Required for interactive features and form validation</td>
                        </tr>
                    </tbody>
                </table>

                <h2>🏫 Business Constraints</h2>

                <h3>📚 Educational Compliance</h3>
                <div class="info-box">
                    <ul>
                        <li><strong>Malaysian Education System:</strong> Compliance with national education standards</li>
                        <li><strong>Language Requirements:</strong> Bahasa Melayu as primary interface language</li>
                        <li><strong>Academic Calendar:</strong> Academic year cycle alignment (January-December)</li>
                        <li><strong>Class Structure:</strong> Form-based class structure (Tingkatan 1-5)</li>
                        <li><strong>Grading System:</strong> Malaysian secondary school grading standards (A+ to G)</li>
                    </ul>
                </div>

                <h3>🏢 Operational Requirements</h3>
                <div class="warning-box">
                    <ul>
                        <li><strong>PIBG Integration:</strong> Parent-Teacher Association payment tracking</li>
                        <li><strong>School Hours:</strong> Alignment with Malaysian school schedule</li>
                        <li><strong>Discipline Policies:</strong> Reporting according to school disciplinary policies</li>
                        <li><strong>Communication:</strong> Parent-teacher communication facilitation</li>
                        <li><strong>Data Privacy:</strong> Compliance with Malaysian data protection requirements</li>
                    </ul>
                </div>

                <h2>🔒 Security Constraints</h2>

                <h3>🛡️ Access Control</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Security Aspect</th>
                            <th>Constraint</th>
                            <th>Implementation</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Role-Based Permissions</td>
                            <td>Strictly enforced</td>
                            <td>Database-level and application-level controls</td>
                        </tr>
                        <tr>
                            <td>Password Complexity</td>
                            <td>Mandatory requirements</td>
                            <td>Real-time validation and server-side enforcement</td>
                        </tr>
                        <tr>
                            <td>Account Lockout</td>
                            <td>Non-negotiable policy</td>
                            <td>Automatic lockout after 3 failed attempts</td>
                        </tr>
                        <tr>
                            <td>Audit Trail</td>
                            <td>Required maintenance</td>
                            <td>All administrative actions logged</td>
                        </tr>
                        <tr>
                            <td>Data Encryption</td>
                            <td>In transit and at rest</td>
                            <td>HTTPS for transmission, hashed passwords</td>
                        </tr>
                    </tbody>
                </table>

                <h3>🔐 Data Protection</h3>
                <div class="success-box">
                    <ul>
                        <li><strong>Personal Information:</strong> Encryption in transit using HTTPS</li>
                        <li><strong>Password Storage:</strong> Secure hashing using industry standards</li>
                        <li><strong>Session Security:</strong> Secure session handling with timeout</li>
                        <li><strong>Input Validation:</strong> SQL injection and XSS prevention</li>
                        <li><strong>Regular Updates:</strong> Security patches and system updates</li>
                        <li><strong>Backup Procedures:</strong> Regular data backup and recovery procedures</li>
                    </ul>
                </div>

                <h2>📋 Implementation Guidelines</h2>

                <h3>🚀 Deployment Considerations</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Aspect</th>
                            <th>Consideration</th>
                            <th>Recommendation</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Environment Setup</td>
                            <td>Development, Testing, Production</td>
                            <td>Separate environments with identical configurations</td>
                        </tr>
                        <tr>
                            <td>Database Migration</td>
                            <td>Schema updates and data migration</td>
                            <td>Version-controlled migration scripts</td>
                        </tr>
                        <tr>
                            <td>Performance Monitoring</td>
                            <td>System performance tracking</td>
                            <td>Regular monitoring of response times and resource usage</td>
                        </tr>
                        <tr>
                            <td>User Training</td>
                            <td>Staff and user education</td>
                            <td>Comprehensive training program for all user roles</td>
                        </tr>
                        <tr>
                            <td>Support Documentation</td>
                            <td>User manuals and help guides</td>
                            <td>Role-specific documentation and troubleshooting guides</td>
                        </tr>
                    </tbody>
                </table>

                <div class="info-box">
                    <strong>📞 Support and Maintenance:</strong>
                    <ul>
                        <li><strong>Technical Support:</strong> Dedicated support team for system issues</li>
                        <li><strong>Regular Maintenance:</strong> Scheduled maintenance windows for updates</li>
                        <li><strong>User Feedback:</strong> Continuous improvement based on user feedback</li>
                        <li><strong>Documentation Updates:</strong> Regular updates to system documentation</li>
                        <li><strong>Training Programs:</strong> Ongoing training for new features and updates</li>
                    </ul>
                </div>
            </section>

            <!-- Document Footer -->
            <footer style="background: #34495e; color: white; padding: 30px; margin-top: 50px; text-align: center;">
                <div style="margin-bottom: 20px;">
                    <h3 style="color: white; margin-bottom: 15px;">📄 Document Information</h3>
                    <div style="display: flex; justify-content: space-around; flex-wrap: wrap; gap: 20px;">
                        <div>
                            <strong>Title:</strong><br>
                            SMKTMI School Management System<br>
                            Analysis & Design Documentation
                        </div>
                        <div>
                            <strong>Version:</strong><br>
                            1.0
                        </div>
                        <div>
                            <strong>Date:</strong><br>
                            December 2024
                        </div>
                        <div>
                            <strong>Classification:</strong><br>
                            Internal Use
                        </div>
                    </div>
                </div>

                <div style="border-top: 1px solid #5d6d7e; padding-top: 20px; margin-top: 20px;">
                    <table style="width: 100%; margin: 0; box-shadow: none; background: transparent;">
                        <thead>
                            <tr>
                                <th style="background: #2c3e50; color: white;">Version</th>
                                <th style="background: #2c3e50; color: white;">Date</th>
                                <th style="background: #2c3e50; color: white;">Author</th>
                                <th style="background: #2c3e50; color: white;">Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr style="background: #34495e;">
                                <td style="color: white; border-bottom: 1px solid #5d6d7e;">1.0</td>
                                <td style="color: white; border-bottom: 1px solid #5d6d7e;">December 2024</td>
                                <td style="color: white; border-bottom: 1px solid #5d6d7e;">System Analysis Team</td>
                                <td style="color: white; border-bottom: 1px solid #5d6d7e;">Initial comprehensive documentation</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div style="margin-top: 20px; font-size: 0.9em; opacity: 0.8;">
                    <p><strong>⚠️ Confidentiality Notice:</strong></p>
                    <p>This document contains confidential and proprietary information.<br>
                    Distribution is restricted to authorized personnel only.</p>
                </div>
            </footer>
        </main>
    </div>

    <!-- JavaScript for smooth scrolling and navigation -->
    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Highlight current section in navigation
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('.section');
            const navLinks = document.querySelectorAll('.nav-menu a');

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (scrollY >= (sectionTop - 100)) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });

        // Print functionality
        function printDocument() {
            window.print();
        }

        // Add print button to navigation
        document.addEventListener('DOMContentLoaded', function() {
            const navMenu = document.querySelector('.nav-menu');
            const printButton = document.createElement('li');
            printButton.innerHTML = '<a href="#" onclick="printDocument(); return false;">🖨️ Print</a>';
            navMenu.appendChild(printButton);
        });
    </script>
</body>
</html>
