<?php
session_start();

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Student') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';

$userId = $_SESSION['user_id'];

// Get student ID
$stmt = $conn->prepare("SELECT student_id, full_name FROM students WHERE user_id = ?");
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();
$student = $result->fetch_assoc();

if (!$student) {
    die("Maklumat pelajar tidak dijumpai.");
}

$studentId = $student['student_id'];

// Get assessment results with subject information
// Your actual table structure:
// assessment: assessment_id, assessment_type, assessment_date, subject_id, classroom_id
// assessment_result: result_id, student_id, assessment_id, marks

$assessmentResult = null;
$hasAssessments = false;

// Check if assessment tables exist and query works
try {
    // First try with 'assessment_result' (singular)
    $assessmentStmt = $conn->prepare("
        SELECT a.assessment_type, a.assessment_date,
               ar.marks,
               s.subject_name
        FROM assessment_result ar
        JOIN assessment a ON ar.assessment_id = a.assessment_id
        JOIN subjects s ON a.subject_id = s.subject_id
        WHERE ar.student_id = ?
        ORDER BY a.assessment_date DESC, s.subject_name
    ");
    $assessmentStmt->bind_param("i", $studentId);
    $assessmentStmt->execute();
    $assessmentResult = $assessmentStmt->get_result();
    $hasAssessments = true;
} catch (mysqli_sql_exception $e) {
    // Try with 'assessment_results' (plural) if singular fails
    try {
        $assessmentStmt = $conn->prepare("
            SELECT a.assessment_type, a.assessment_date,
                   ar.marks,
                   s.subject_name
            FROM assessment_results ar
            JOIN assessment a ON ar.assessment_id = a.assessment_id
            JOIN subjects s ON a.subject_id = s.subject_id
            WHERE ar.student_id = ?
            ORDER BY a.assessment_date DESC, s.subject_name
        ");
        $assessmentStmt->bind_param("i", $studentId);
        $assessmentStmt->execute();
        $assessmentResult = $assessmentStmt->get_result();
        $hasAssessments = true;
    } catch (mysqli_sql_exception $e2) {
        // Assessment tables don't exist or have different structure
        $hasAssessments = false;
    }
}

// Function to calculate grade based on marks
function calculateGrade($marks) {
    if ($marks >= 90) return 'A+';
    if ($marks >= 80) return 'A';
    if ($marks >= 70) return 'A-';
    if ($marks >= 65) return 'B+';
    if ($marks >= 60) return 'B';
    if ($marks >= 55) return 'C+';
    if ($marks >= 50) return 'C';
    if ($marks >= 45) return 'D';
    if ($marks >= 40) return 'E';
    return 'F';
}

// Organize assessment data into a grid format
$assessmentGrid = [];
$assessmentTypes = [];
$subjects = [];

if ($hasAssessments && $assessmentResult) {
    while ($row = $assessmentResult->fetch_assoc()) {
        $subject = $row['subject_name'];
        $assessmentType = $row['assessment_type'];
        $marks = (int)$row['marks'];
        $grade = calculateGrade($marks);

        // Collect unique subjects and assessment types
        if (!in_array($subject, $subjects)) {
            $subjects[] = $subject;
        }
        if (!in_array($assessmentType, $assessmentTypes)) {
            $assessmentTypes[] = $assessmentType;
        }

        // Store in grid format: [subject][assessment_type] = [marks, grade]
        $assessmentGrid[$subject][$assessmentType] = [
            'marks' => $marks,
            'grade' => $grade
        ];
    }

    // Sort for better display
    sort($subjects);
    sort($assessmentTypes);
}

include 'includes/header.php';
?>

<!-- FontAwesome CDN for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<style>
/* Content Styling */
.content {
    margin-left: 300px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.page-container {
    max-width: 1200px;
    margin: 0 auto;
}

.page-header {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 30px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.page-header h2 {
    margin: 0;
    font-size: 28px;
    font-weight: 700;
}

.assessment-grid-table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    margin-top: 30px;
}

.grid-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.grid-table th {
    background-color: rgb(153, 156, 158);
    color: white;
    padding: 12px 8px;
    text-align: center;
    font-weight: 600;
    border: 1px solid rgb(153, 156, 158);
    font-size: 12px;
}

.grid-table td {
    padding: 8px;
    text-align: center;
    border: 1px solid #dee2e6;
    vertical-align: middle;
}

.subject-cell {
    background-color:rgb(153, 156, 158)
    font-weight: 600;
    text-align: left;
    padding-left: 15px;
    color: #495057;
}

.number-cell {
    background-color: rgb(153, 156, 158);
    color: white;
    font-weight: 600;
    width: 40px;
}

.marks-cell {
    background-color: #fff;
    font-weight: 600;
    color: #495057;
    min-width: 50px;
}

.grade-cell {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #6c757d;
    min-width: 40px;
}

.empty-cell {
    background-color: #e9ecef;
    color: #adb5bd;
}

.assessment-header {
    background-color: rgb(153, 156, 158) !important;
    color: white !important;
    font-weight: 700;
    font-size: 11px;
    padding: 8px 4px;
}

.sub-header {
    background-color: #495057 !important;
    color: white !important;
    font-weight: 600;
    font-size: 10px;
    padding: 6px 4px;
}

/* Removed old table styles - using new grid design */

.no-assessments {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.no-assessments-icon {
    font-size: 64px;
    color: #bdc3c7;
    margin-bottom: 20px;
}

/* Removed filter and stat card styles - using new grid design */

/* Responsive */
@media (max-width: 768px) {
    .content {
        margin-left: 0;
        padding: 20px;
    }
    .grid-table {
        font-size: 12px;
    }
    .grid-table th, .grid-table td {
        padding: 6px 4px;
    }
    .subject-cell {
        padding-left: 8px;
    }
    .assessment-header {
        font-size: 10px;
        padding: 6px 2px;
    }
    .sub-header {
        font-size: 9px;
        padding: 4px 2px;
    }
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
    <div class="page-container">
        <!-- Page Header -->
        <div class="page-header">
            <h2><i class="fas fa-clipboard-list"></i> Penilaian Saya</h2>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">Rekod penilaian dan markah untuk <?php echo htmlspecialchars($student['full_name']); ?></p>
        </div>

        <?php if ($hasAssessments && !empty($subjects)): ?>
            <!-- Assessment Grid Table -->
            <div class="assessment-grid-table">
                <table class="grid-table">
                    <thead>
                        <tr>
                            <th rowspan="2" class="number-cell">Bil</th>
                            <th rowspan="2" class="subject-cell" style="text-align: center; background-color:rgb(153, 156, 158); color: white;">Mata Pelajaran</th>
                            <?php foreach ($assessmentTypes as $type): ?>
                                <th colspan="2" class="assessment-header"><?php echo strtoupper(htmlspecialchars($type)); ?></th>
                            <?php endforeach; ?>
                        </tr>
                        <tr>
                            <?php foreach ($assessmentTypes as $type): ?>
                                <th class="sub-header">Markah</th>
                                <th class="sub-header">Gred</th>
                            <?php endforeach; ?>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $bil = 1; foreach ($subjects as $subject): ?>
                            <tr>
                                <td class="number-cell"><?php echo $bil++; ?>.</td>
                                <td class="subject-cell"><?php echo strtoupper(htmlspecialchars($subject)); ?></td>
                                <?php foreach ($assessmentTypes as $type): ?>
                                    <?php if (isset($assessmentGrid[$subject][$type])): ?>
                                        <td class="marks-cell"><?php echo $assessmentGrid[$subject][$type]['marks']; ?></td>
                                        <td class="grade-cell"><?php echo $assessmentGrid[$subject][$type]['grade']; ?></td>
                                    <?php else: ?>
                                        <td class="empty-cell">-</td>
                                        <td class="empty-cell">-</td>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="no-assessments">
                <div class="no-assessments-icon">
                    <i class="fas fa-clipboard-list"></i>
                </div>
                <h3>Tiada Rekod Penilaian</h3>
                <p>Sistem penilaian belum dilaksanakan atau tiada markah ujian/peperiksaan yang direkodkan lagi.</p>
                <p><small>Sila hubungi guru atau pihak pentadbiran untuk maklumat lanjut.</small></p>
            </div>
        <?php endif; ?>
    </div>
</div>

</body>
</html>
