<?php
session_start();
require '../db.php';

// Check if the user is logged in
/*if (isset($_SESSION['user_id'])) {
    $userId = $_SESSION['user_id'];
    $currentTimestamp = date('Y-m-d H:i:s'); // Current date and time

    // Update last_logout in the admin table if the user is an admin
    if ($_SESSION['role'] === 'Admin') {
        $updateStmt = $conn->prepare("UPDATE admins SET last_logout = ? WHERE user_id = ?");
        $updateStmt->bind_param("si", $currentTimestamp, $userId);
        $updateStmt->execute();
    }

    
}*/ // Tambah Komen 8/5/2025 - 1.30AM Sebab Logout Ni Copy Dari Logout Admin
    // So Tak Perlu Check Login ATau Tidak

// Clear all session variables
$_SESSION = [];

// Unset all session variables
session_unset();
    
// Destroy the session
session_destroy();  

// Redirect to login page
header("Location: ../login.php");
exit;