<?php
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';
require '../includes/universal_logger.php';

$message = "";
$edit_mode = false;
$edit_classroom = null;

// Handle delete request
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $classroom_id = (int)$_GET['delete'];

    // Check if classroom has any students assigned
    $check_students = $conn->prepare("SELECT COUNT(*) as count FROM students WHERE classroom_id = ?");
    $check_students->bind_param("i", $classroom_id);
    $check_students->execute();
    $student_result = $check_students->get_result();
    $student_count = $student_result->fetch_assoc()['count'];
    $check_students->close();

    // Check if classroom has any teacher assignments
    $check_assignments = $conn->prepare("SELECT COUNT(*) as count FROM teacher_subject_classrooms WHERE classroom_id = ?");
    $check_assignments->bind_param("i", $classroom_id);
    $check_assignments->execute();
    $assignment_result = $check_assignments->get_result();
    $assignment_count = $assignment_result->fetch_assoc()['count'];
    $check_assignments->close();

    // Check if classroom has any schedules
    $check_schedules = $conn->prepare("SELECT COUNT(*) as count FROM schedules WHERE classroom_id = ?");
    $check_schedules->bind_param("i", $classroom_id);
    $check_schedules->execute();
    $schedule_result = $check_schedules->get_result();
    $schedule_count = $schedule_result->fetch_assoc()['count'];
    $check_schedules->close();

    if ($student_count > 0 || $assignment_count > 0 || $schedule_count > 0) {
        $message = "❌ Tidak boleh padam kelas ini kerana masih mempunyai pelajar ($student_count), tugasan guru ($assignment_count), atau jadual ($schedule_count) yang ditetapkan.";
    } else {
        // Get classroom info for logging
        $classroom_stmt = $conn->prepare("SELECT class_name, tingkatan FROM classrooms WHERE classroom_id = ?");
        $classroom_stmt->bind_param("i", $classroom_id);
        $classroom_stmt->execute();
        $classroom_result = $classroom_stmt->get_result();
        $classroom_info = $classroom_result->fetch_assoc();
        $classroom_stmt->close();

        if ($classroom_info) {
            // Delete classroom record
            $delete_classroom = $conn->prepare("DELETE FROM classrooms WHERE classroom_id = ?");
            $delete_classroom->bind_param("i", $classroom_id);

            if ($delete_classroom->execute()) {
                // Log the deletion with details
                $details = [
                    'Nama Kelas' => $classroom_info['class_name'],
                    'Tingkatan' => $classroom_info['tingkatan'],
                    'Sebab Padam' => 'Tiada pelajar, guru atau jadual yang berkaitan'
                ];

                logAdminActivity($conn, 'DELETE', 'CLASSROOM', $classroom_id, $classroom_info['class_name'],
                    "Kelas dipadam", $details);

                $message = "✅ Kelas " . htmlspecialchars($classroom_info['class_name']) . " berjaya dipadam.";
            } else {
                $message = "❌ Ralat semasa memadam kelas: " . $delete_classroom->error;
            }
            $delete_classroom->close();
        } else {
            $message = "❌ Kelas tidak dijumpai.";
        }
    }
}

// Check if we're in edit mode
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $edit_mode = true;
    $edit_id = (int)$_GET['edit'];

    // Fetch classroom data for editing
    $stmt = $conn->prepare("SELECT * FROM classrooms WHERE classroom_id = ?");
    $stmt->bind_param("i", $edit_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 1) {
        $edit_classroom = $result->fetch_assoc();
    } else {
        $message = "❌ Kelas tidak dijumpai.";
        $edit_mode = false;
    }
    $stmt->close();
}

// Handle form submission (both add and edit)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['class_name'])) {
    $class_name = trim($_POST['class_name']);
    $tingkatan = null;
    $classroom_id = isset($_POST['classroom_id']) ? (int)$_POST['classroom_id'] : 0;

    // Validate input
    if (empty($class_name)) {
        $message = "❌ Nama kelas tidak boleh kosong.";
    } else {
        // Extract tingkatan from class name
        if (preg_match('/Tingkatan\s+(\d+)/i', $class_name, $matches)) {
            $tingkatan = (int)$matches[1];

            if ($tingkatan < 1 || $tingkatan > 6) {
                $message = "❌ Tingkatan mestilah antara 1 hingga 6 sahaja.";
                $tingkatan = null;
            }
        } else {
            $message = "❌ Gagal kenal pasti tingkatan. Gunakan format 'Tingkatan X Nama'.";
        }
    }

    if ($tingkatan !== null) {
        if ($classroom_id > 0) {
            // Edit mode - check for duplicate excluding current classroom
            $stmt_check = $conn->prepare("SELECT COUNT(*) FROM classrooms WHERE class_name = ? AND classroom_id != ?");
            $stmt_check->bind_param("si", $class_name, $classroom_id);
            $stmt_check->execute();
            $stmt_check->bind_result($count);
            $stmt_check->fetch();
            $stmt_check->close();

            if ($count > 0) {
                $message = "❌ Nama kelas telah wujud.";
            } else {
                // Update classroom
                $stmt = $conn->prepare("UPDATE classrooms SET class_name = ?, tingkatan = ? WHERE classroom_id = ?");
                $stmt->bind_param("sii", $class_name, $tingkatan, $classroom_id);

                if ($stmt->execute()) {
                    // Log the classroom update
                    logAdminActivity($conn, 'UPDATE', 'CLASSROOM', $classroom_id, $class_name,
                        "Kelas dikemaskini: $class_name (Tingkatan $tingkatan)");

                    header("Location: add_classroom.php?success=2");
                    exit;
                } else {
                    $message = "❌ Ralat kemaskini: " . $stmt->error;
                }
                $stmt->close();
            }
        } else {
            // Add mode - check for duplicate class
            $stmt_check = $conn->prepare("SELECT COUNT(*) FROM classrooms WHERE class_name = ?");
            $stmt_check->bind_param("s", $class_name);
            $stmt_check->execute();
            $stmt_check->bind_result($count);
            $stmt_check->fetch();
            $stmt_check->close();

            if ($count > 0) {
                $message = "❌ Nama kelas telah wujud.";
            } else {
                $stmt = $conn->prepare("INSERT INTO classrooms (class_name, tingkatan) VALUES (?, ?)");
                if (!$stmt) {
                    die("Gagal sediakan statement: " . $conn->error);
                }
                $stmt->bind_param("si", $class_name, $tingkatan);

                if ($stmt->execute()) {
                    // Log the classroom creation
                    $new_classroom_id = $conn->insert_id;
                    logAdminActivity($conn, 'CREATE', 'CLASSROOM', $new_classroom_id, $class_name,
                        "Kelas baru dicipta: $class_name (Tingkatan $tingkatan)");

                    header("Location: add_classroom.php?success=1");
                    exit;
                } else {
                    $message = "❌ Ralat: " . $stmt->error;
                }
                $stmt->close();
            }
        }
    }
}

// Pagination setup
$limit = 10;
$page = isset($_GET['page']) && is_numeric($_GET['page']) && $_GET['page'] > 0 ? (int) $_GET['page'] : 1;

// If we're in edit mode, find which page the classroom is on
if ($edit_mode && $edit_classroom) {
    // Count how many classrooms come before this one alphabetically
    $count_before_stmt = $conn->prepare("SELECT COUNT(*) as count FROM classrooms WHERE class_name < ?");
    $count_before_stmt->bind_param("s", $edit_classroom['class_name']);
    $count_before_stmt->execute();
    $count_result = $count_before_stmt->get_result();
    $count_row = $count_result->fetch_assoc();
    $classrooms_before = $count_row['count'];
    $count_before_stmt->close();

    // Calculate which page this classroom should be on
    $correct_page = floor($classrooms_before / $limit) + 1;

    // If we're not on the correct page and not processing a form, redirect to it
    if ($page != $correct_page && $_SERVER['REQUEST_METHOD'] !== 'POST') {
        header("Location: add_classroom.php?edit=" . $edit_classroom['classroom_id'] . "&page=" . $correct_page);
        exit;
    }
}

$offset = ($page - 1) * $limit;

// Count total classes
$total_result = $conn->query("SELECT COUNT(*) AS total FROM classrooms");
if (!$total_result) {
    die("Error counting classrooms: " . $conn->error);
}
$total_row = $total_result->fetch_assoc();
$total_classes = $total_row['total'];
$total_pages = ceil($total_classes / $limit);

// Ensure page is within valid range
if ($page > $total_pages && $total_pages > 0) {
    $page = $total_pages;
    $offset = ($page - 1) * $limit;
}

// Fetch classrooms with LIMIT
$classrooms = [];
$sql = "SELECT * FROM classrooms ORDER BY class_name ASC LIMIT ? OFFSET ?";
$stmt = $conn->prepare($sql);
if (!$stmt) {
    die("Error preparing statement: " . $conn->error);
}
$stmt->bind_param("ii", $limit, $offset);
$stmt->execute();
$result = $stmt->get_result();
while ($row = $result->fetch_assoc()) {
    $classrooms[] = $row;
}
$stmt->close();
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pengurusan Kelas - SMKTMI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>

<style>
/* Sidebar Styling */
.sidebar {
    position: fixed;
    top: 0; left: 0;
    width: 300px;
    height: 100vh;
    background-color: #2c3e50;
    padding: 30px 20px;
    color: #ecf0f1;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow-y: auto;
    box-shadow: 5px 0 15px rgba(0,0,0,0.4);
    border-right: 1px solid #34495e;
}

.sidebar h4 {
    font-size: 18px;
    font-weight: 700;
    text-transform: uppercase;
    margin: 30px 0 12px;
    border-bottom: 2px solid #3498db;
    padding-bottom: 6px;
    letter-spacing: 1.5px;
}

.sidebar a {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    color: #bdc3c7;
    text-decoration: none;
    font-size: 16px;
    margin-bottom: 8px;
    border-radius: 6px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.sidebar a i {
    margin-right: 12px;
    width: 18px;
    text-align: center;
}

.sidebar a:hover {
    background-color: #2980b9;
    color: #ecf0f1;
}

/* Content Styling */
.content {
    margin-left: 280px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.form-container {
    width: 100%;
    max-width: 1000px;
    background-color: #fff;
    padding: 30px 40px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0 auto;
}

.form-container h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 25px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    color: #2980b9;
    text-align: center;
}

.alert {
    padding: 15px;
    background-color: #d9edf7;
    border: 1px solid #bce8f1;
    border-radius: 5px;
    color: #31708f;
    margin-bottom: 25px;
    text-align: center;
}

.alert-success {
    background-color: #dff0d8;
    border-color: #d6e9c6;
    color: #3c763d;
}

.alert-danger {
    background-color: #f2dede;
    border-color: #ebccd1;
    color: #a94442;
}

/* Form Styling */
form {
    margin-bottom: 30px;
}

label {
    font-weight: 700;
    margin-bottom: 6px;
    display: block;
    color: #34495e;
}

input[type="text"] {
    width: 100%;
    padding: 10px 14px;
    font-size: 16px;
    border: 1.5px solid #ccc;
    border-radius: 6px;
    transition: border-color 0.3s ease;
    margin-bottom: 15px;
}

input:focus {
    border-color: #2980b9;
    outline: none;
}

button[type="submit"] {
    padding: 12px 30px;
    background-color: #2980b9;
    border: none;
    color: #ecf0f1;
    font-size: 16px;
    font-weight: 700;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    display: block;
    margin: 20px auto 0;
}

button[type="submit"]:hover {
    background-color: #1a5d8f;
}

/* Table Styling */
.table {
    margin-top: 30px;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

.table tr.editing {
    background-color: #fff3cd !important;
    border-left: 4px solid #f39c12;
}

.table tr.editing td {
    font-weight: 600;
}

.action-btn {
    padding: 6px 12px;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    font-size: 14px;
    display: inline-block;
    transition: all 0.3s ease;
}

.action-btn:hover {
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: flex-start;
}

.btn-edit {
    background-color: #3498db;
    color: white;
    padding: 6px 12px;
    text-decoration: none;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s ease;
}

.btn-edit:hover {
    background-color: #2980b9;
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.btn-delete {
    background-color: #e74c3c;
    color: white;
    padding: 6px 12px;
    text-decoration: none;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s ease;
}

.btn-delete:hover {
    background-color: #c0392b;
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.btn-cancel {
    background-color: #e74c3c;
    color: white;
    padding: 6px 12px;
    text-decoration: none;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s ease;
}

.btn-cancel:hover {
    background-color: #c0392b;
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Pagination Styling */
.pagination {
    margin-top: 30px;
    display: flex;
    justify-content: center;
    gap: 5px;
    align-items: center;
}

.pagination a, .pagination span {
    padding: 8px 12px;
    border: 1px solid #ddd;
    text-decoration: none;
    color: #3498db;
    border-radius: 4px;
}

.pagination a:hover {
    background-color: #3498db;
    color: white;
}

.pagination .active {
    background-color: #3498db;
    color: white;
    border-color: #3498db;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
        box-shadow: none;
    }
    .content {
        margin-left: 0;
        padding: 20px;
    }
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
  <div class="form-container">
    <h2>Pengurusan Kelas</h2>

    <?php if (isset($_GET['success'])): ?>
      <?php if ($_GET['success'] == '1'): ?>
        <div class="alert alert-success">✅ Kelas berjaya ditambah.</div>
      <?php elseif ($_GET['success'] == '2'): ?>
        <div class="alert alert-success">✅ Kelas berjaya dikemaskini.</div>
      <?php endif; ?>
    <?php elseif (!empty($message)): ?>
      <div class="alert alert-danger"><?php echo htmlspecialchars($message); ?></div>
    <?php endif; ?>

    <!-- Add/Edit Class Form -->
    <h4 style="margin-top: 30px; margin-bottom: 20px; text-align: center; color: #2c3e50;">
      <?php echo $edit_mode ? 'Kemaskini Kelas' : 'Tambah Kelas Baharu'; ?>
    </h4>
    <form method="post">
      <?php if ($edit_mode): ?>
        <input type="hidden" name="classroom_id" value="<?php echo $edit_classroom['classroom_id']; ?>">
      <?php endif; ?>

      <label for="class_name">Nama Kelas</label>
      <input type="text" name="class_name" id="class_name" required
        placeholder="Contoh: Tingkatan 1 Hanafi"
        value="<?php echo $edit_mode ? htmlspecialchars($edit_classroom['class_name']) : (isset($_POST['class_name']) ? htmlspecialchars($_POST['class_name']) : ''); ?>">

      <div style="text-align: center; margin-top: 20px;">
        <button type="submit"><?php echo $edit_mode ? 'Kemaskini Kelas' : 'Tambah Kelas'; ?></button>
        <?php if ($edit_mode): ?>
          <a href="add_classroom.php" class="action-btn btn-cancel" style="margin-left: 15px; padding: 12px 30px; font-weight: 700;">Batal</a>
        <?php endif; ?>
      </div>
    </form>

      <!-- Classroom List -->
      <?php if (count($classrooms) > 0): ?>
        <table class="table table-bordered table-striped">
          <thead class="table-light">
            <tr>
              <th>Bil.</th>
              <th>Nama Kelas</th>
              <th>Tingkatan</th>
              <th>Guru Kelas</th>
              <th>No. Telefon</th>
              <th style="width: 160px;">Tindakan</th>
            </tr>
          </thead>
          <tbody>
            <?php foreach ($classrooms as $index => $classroom): ?>
              <tr<?php echo ($edit_mode && $edit_classroom && $classroom['classroom_id'] == $edit_classroom['classroom_id']) ? ' class="editing"' : ''; ?>>
                <td><?php echo $offset + $index + 1; ?></td>
                <td><?php echo htmlspecialchars($classroom['class_name']); ?></td>
                <td><?php echo htmlspecialchars($classroom['tingkatan']); ?></td>
                <?php
                if (!empty($classroom['teacher_id'])) {
                  $teacher_id = (int)$classroom['teacher_id'];
                  $teacher_result = $conn->query("SELECT full_name, phone_number FROM teachers WHERE teacher_id = $teacher_id");
                  if ($teacher_result && $teacher_result->num_rows > 0) {
                    $teacher = $teacher_result->fetch_assoc();
                    $teacher_name = htmlspecialchars($teacher['full_name']);
                    $teacher_phone = htmlspecialchars($teacher['phone_number']);
                  } else {
                    $teacher_name = "Belum ditetapkan";
                    $teacher_phone = "-";
                  }
                } else {
                  $teacher_name = "Belum ditetapkan";
                  $teacher_phone = "-";
                }
                ?>
                <td><?php echo $teacher_name; ?></td>
                <td><?php echo $teacher_phone; ?></td>
                <td>
                  <div class="action-buttons">
                    <a href="add_classroom.php?edit=<?php echo $classroom['classroom_id']; ?>"
                       class="btn-edit">
                      <i class="fas fa-edit"></i> Edit
                    </a>
                    <button onclick="confirmDeleteClassroom(<?php echo $classroom['classroom_id']; ?>, '<?php echo htmlspecialchars($classroom['class_name'], ENT_QUOTES); ?>')"
                            class="btn-delete">
                      <i class="fas fa-trash"></i> Padam
                    </button>
                  </div>
                </td>
              </tr>
            <?php endforeach; ?>
          </tbody>
        </table>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
          <div class="pagination">
            <?php
            // Build query string for pagination
            $query_params = [];
            if (isset($_GET['edit'])) {
                $query_params['edit'] = $_GET['edit'];
            }
            $query_string = !empty($query_params) ? '&' . http_build_query($query_params) : '';
            ?>

            <?php if ($page > 1): ?>
              <a href="?page=<?= $page - 1 ?><?= $query_string ?>">&laquo; Sebelumnya</a>
            <?php else: ?>
              <span style="color: #bdc3c7; cursor: not-allowed;">&laquo; Sebelumnya</span>
            <?php endif; ?>

            <?php for ($i = 1; $i <= $total_pages; $i++): ?>
              <?php if ($i === $page): ?>
                <span class="active"><?= $i ?></span>
              <?php else: ?>
                <a href="?page=<?= $i ?><?= $query_string ?>"><?= $i ?></a>
              <?php endif; ?>
            <?php endfor; ?>

            <?php if ($page < $total_pages): ?>
              <a href="?page=<?= $page + 1 ?><?= $query_string ?>">Seterusnya &raquo;</a>
            <?php else: ?>
              <span style="color: #bdc3c7; cursor: not-allowed;">Seterusnya &raquo;</span>
            <?php endif; ?>
          </div>
        <?php endif; ?>
      <?php else: ?>
        <p style="text-align: center; color: #7f8c8d; font-style: italic; padding: 40px;">Tiada kelas direkodkan.</p>
      <?php endif; ?>
  </div>
</div>

<script>
function confirmDeleteClassroom(classroomId, className) {
    if (confirm('Adakah anda pasti ingin memadam kelas "' + className + '"?\n\nTindakan ini tidak boleh dibatalkan dan akan memadam semua data berkaitan kelas ini.')) {
        window.location.href = 'add_classroom.php?delete=' + classroomId;
    }
}
</script>

</body>
</html>
