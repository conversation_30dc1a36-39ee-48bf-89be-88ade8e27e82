<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';

// Check if admin_logs table exists
$tableCheck = $conn->query("SHOW TABLES LIKE 'admin_logs'");

$message = "";
$success = false;

if ($tableCheck && $tableCheck->num_rows > 0) {
    $message = "✅ Jadual admin_logs sudah wujud dan sedia digunakan!";
    $success = true;
} else {
    $message = "❌ Jadual admin_logs tidak wujud. Sila hubungi pentadbir sistem untuk menyediakan jadual pangkalan data.";
    $success = false;
}

include 'includes/header.php';
?>

<!-- FontAwesome CDN for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<style>
/* Content Styling */
.content {
    margin-left: 280px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.setup-container {
    width: 100%;
    max-width: 800px;
    background-color: #fff;
    padding: 30px 40px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0 auto;
}

.setup-container h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 25px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    color: #2980b9;
    text-align: center;
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
    font-weight: 600;
}

.alert-success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.alert-error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    margin-right: 10px;
    font-size: 14px;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
    color: white;
    text-decoration: none;
}

.btn-secondary {
    background-color: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
    color: white;
    text-decoration: none;
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
    <div class="setup-container">
        <h2><i class="fas fa-database"></i> Setup Admin Logs Table</h2>
        
        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $success ? 'success' : 'error'; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <div style="text-align: center; margin-top: 30px;">
            <?php if ($success): ?>
                <p>Jadual admin_logs sudah sedia dan anda boleh melihat log aktiviti admin.</p>
                <a href="view_admin_logs.php" class="btn btn-primary">
                    <i class="fas fa-list"></i> Lihat Log Admin
                </a>
            <?php else: ?>
                <p>Sila hubungi pentadbir sistem untuk menyediakan jadual yang diperlukan.</p>
            <?php endif; ?>
            <a href="dashboard.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali ke Dashboard
            </a>
        </div>
    </div>
</div>

</body>
</html>
