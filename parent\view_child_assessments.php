<?php
session_start();

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Parent') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';

$parent_user_id = $_SESSION['user_id'];
$stmt = $conn->prepare("SELECT parent_id FROM parents WHERE user_id = ?");
$stmt->bind_param("i", $parent_user_id);
$stmt->execute();
$result = $stmt->get_result();
$parent = $result->fetch_assoc();

if (!$parent) {
    die("Maklumat ibu bapa tidak dijumpai. Sila hubungi pentadbir sistem.");
}

$parent_id = $parent['parent_id'];

// Get children
$childrenStmt = $conn->prepare("
    SELECT s.student_id, s.full_name, c.class_name 
    FROM students s 
    LEFT JOIN classrooms c ON s.classroom_id = c.classroom_id 
    WHERE s.parent_id = ? 
    ORDER BY s.full_name
");
$childrenStmt->bind_param("i", $parent_id);
$childrenStmt->execute();
$children = $childrenStmt->get_result();

// Get selected child and assessment type
$selectedChild = $_GET['student_id'] ?? '';
$selectedAssessmentType = $_GET['assessment_type'] ?? '';

// Get available assessment types for the selected child
$assessmentTypes = [];
$childInfo = null;
if ($selectedChild) {
    // Verify child belongs to this parent
    $verifyStmt = $conn->prepare("
        SELECT s.full_name, c.class_name
        FROM students s
        LEFT JOIN classrooms c ON s.classroom_id = c.classroom_id
        WHERE s.student_id = ? AND s.parent_id = ?
    ");
    $verifyStmt->bind_param("ii", $selectedChild, $parent_id);
    $verifyStmt->execute();
    $childInfo = $verifyStmt->get_result()->fetch_assoc();

    if ($childInfo) {
        // Get available assessment types for this child
        $assessmentTypesStmt = $conn->prepare("
            SELECT DISTINCT a.assessment_type, a.assessment_date
            FROM assessment_result ar
            JOIN assessment a ON ar.assessment_id = a.assessment_id
            WHERE ar.student_id = ?
            ORDER BY a.assessment_date DESC, a.assessment_type
        ");
        $assessmentTypesStmt->bind_param("i", $selectedChild);
        $assessmentTypesStmt->execute();
        $assessmentTypes = $assessmentTypesStmt->get_result();
    }
}

// Get child assessments based on filters
$assessments = [];
if ($selectedChild && $selectedAssessmentType && $childInfo) {
    // Get assessments for specific assessment type
    $assessmentStmt = $conn->prepare("
        SELECT a.assessment_date, a.assessment_type, s.subject_name, ar.marks,
               CASE
                   WHEN ar.marks >= 90 THEN 'A+'
                   WHEN ar.marks >= 80 THEN 'A'
                   WHEN ar.marks >= 70 THEN 'A-'
                   WHEN ar.marks >= 65 THEN 'B+'
                   WHEN ar.marks >= 60 THEN 'B'
                   WHEN ar.marks >= 55 THEN 'C+'
                   WHEN ar.marks >= 50 THEN 'C'
                   WHEN ar.marks >= 45 THEN 'D'
                   WHEN ar.marks >= 40 THEN 'E'
                   ELSE 'G'
               END as grade
        FROM assessment_result ar
        JOIN assessment a ON ar.assessment_id = a.assessment_id
        JOIN subjects s ON a.subject_id = s.subject_id
        WHERE ar.student_id = ? AND a.assessment_type = ?
        ORDER BY s.subject_name
    ");
    $assessmentStmt->bind_param("is", $selectedChild, $selectedAssessmentType);
    $assessmentStmt->execute();
    $assessments = $assessmentStmt->get_result();
}
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markah Anak - SMKTMI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>

<style>
/* Content Styling */
.content {
    margin-left: 280px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.form-container {
    width: 100%;
    max-width: 1200px;
    background-color: #fff;
    padding: 30px 40px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0 auto;
}

.form-container h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 25px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    color: #2980b9;
    text-align: center;
}

.filter-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
    border-left: 4px solid #3498db;
}

.btn-primary {
    background-color: #3498db;
    border-color: #3498db;
    padding: 10px 30px;
    font-weight: 600;
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

.assessment-table th {
    background-color: rgb(153, 156, 158);
    color: white;
    text-align: center;
    font-weight: 600;
    padding: 12px;
    border: 1px solid #dee2e6;
}

.assessment-table td {
    padding: 12px;
    border: 1px solid #dee2e6;
    vertical-align: middle;
}

.assessment-table tbody tr:nth-child(even) {
    background-color: #f8f9fa;
}

.assessment-table tbody tr:hover {
    background-color: #e9ecef;
}

/* Responsive */
@media (max-width: 768px) {
    .content {
        margin-left: 0;
        padding: 20px;
    }
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
  <div class="form-container">
    <h2><i class="fas fa-chart-line"></i> Markah Penilaian Anak</h2>

    <!-- Filter Section -->
    <div class="filter-section">
      <form method="GET" class="row g-3" id="filterForm">
        <div class="col-md-6">
          <label class="form-label"><strong>Pilih Anak:</strong></label>
          <select name="student_id" class="form-select" onchange="handleChildChange()">
            <option value="">-- Pilih Anak --</option>
            <?php while ($child = $children->fetch_assoc()): ?>
              <option value="<?php echo $child['student_id']; ?>"
                      <?php echo ($selectedChild == $child['student_id']) ? 'selected' : ''; ?>>
                <?php echo htmlspecialchars($child['full_name']); ?>
                <?php if ($child['class_name']): ?>
                  - <?php echo htmlspecialchars($child['class_name']); ?>
                <?php endif; ?>
              </option>
            <?php endwhile; ?>
          </select>
        </div>

        <?php if ($selectedChild && $assessmentTypes->num_rows > 0): ?>
        <div class="col-md-6">
          <label class="form-label"><strong>Pilih Jenis Penilaian:</strong></label>
          <select name="assessment_type" class="form-select" onchange="this.form.submit()">
            <option value="">-- Pilih Jenis Penilaian --</option>
            <?php while ($type = $assessmentTypes->fetch_assoc()): ?>
              <option value="<?php echo htmlspecialchars($type['assessment_type']); ?>"
                      <?php echo ($selectedAssessmentType == $type['assessment_type']) ? 'selected' : ''; ?>>
                <?php echo htmlspecialchars($type['assessment_type']); ?>
                (<?php echo date('d/m/Y', strtotime($type['assessment_date'])); ?>)
              </option>
            <?php endwhile; ?>
          </select>
        </div>
        <?php endif; ?>
      </form>
    </div>

    <?php if ($selectedChild && $childInfo): ?>
    <!-- Child Info -->
    <div class="alert alert-info">
      <h5><i class="fas fa-child"></i> Maklumat Anak</h5>
      <p><strong>Nama:</strong> <?php echo htmlspecialchars($childInfo['full_name']); ?></p>
      <p><strong>Kelas:</strong> <?php echo htmlspecialchars($childInfo['class_name'] ?? 'Belum Ditetapkan'); ?></p>
      <?php if ($selectedAssessmentType): ?>
      <p><strong>Jenis Penilaian:</strong> <?php echo htmlspecialchars($selectedAssessmentType); ?></p>
      <?php endif; ?>
    </div>

    <!-- Assessment Results -->
    <?php if ($selectedAssessmentType && $assessments->num_rows > 0): ?>
    <div class="table-responsive">
      <table class="table table-bordered assessment-table">
        <thead>
          <tr>
            <th>Bil</th>
            <th>Subjek</th>
            <th>Markah</th>
            <th>Gred</th>
          </tr>
        </thead>
        <tbody>
          <?php $bil = 1; while ($assessment = $assessments->fetch_assoc()): ?>
          <tr>
            <td class="text-center"><?php echo $bil++; ?>.</td>
            <td><?php echo htmlspecialchars($assessment['subject_name']); ?></td>
            <td class="text-center"><strong><?php echo (int)$assessment['marks']; ?></strong></td>
            <td class="text-center">
              <span style="color: black; font-weight: bold;">
                <?php echo $assessment['grade']; ?>
              </span>
            </td>
          </tr>
          <?php endwhile; ?>
        </tbody>
      </table>
    </div>
    <?php elseif ($selectedChild && !$selectedAssessmentType): ?>
    <div class="text-center" style="padding: 40px;">
      <i class="fas fa-filter" style="font-size: 48px; color: #bdc3c7; margin-bottom: 15px;"></i>
      <h5 style="color: #7f8c8d;">Sila Pilih Jenis Penilaian</h5>
      <p style="color: #95a5a6;">Pilih jenis penilaian dari senarai di atas untuk melihat markah subjek.</p>
    </div>
    <?php elseif ($selectedAssessmentType && $assessments->num_rows == 0): ?>
    <div class="text-center" style="padding: 40px;">
      <i class="fas fa-clipboard-list" style="font-size: 48px; color: #bdc3c7; margin-bottom: 15px;"></i>
      <h5 style="color: #7f8c8d;">Tiada Rekod Penilaian</h5>
      <p style="color: #95a5a6;">Tiada markah untuk jenis penilaian yang dipilih.</p>
    </div>
    <?php endif; ?>
    <?php elseif ($selectedChild): ?>
    <div class="alert alert-danger">
      <i class="fas fa-exclamation-triangle"></i> Anak yang dipilih tidak sah atau tidak berkaitan dengan akaun anda.
    </div>
    <?php endif; ?>

  </div>
</div>

<script>
function handleChildChange() {
    // Clear assessment type when child changes
    const form = document.getElementById('filterForm');
    const assessmentTypeSelect = form.querySelector('select[name="assessment_type"]');
    if (assessmentTypeSelect) {
        assessmentTypeSelect.value = '';
    }
    form.submit();
}
</script>

</body>
</html>
