body {
    font-family: Arial, sans-serif;
    background: #f2f2f2;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.login-container {
    background: #fff;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 0 12px rgba(0, 0, 0, 0.15);
    width: 100%;
    max-width: 400px;
}

h2 {
    text-align: center;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 15px;
}

label {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
}

input[type="email"],
input[type="password"] {
    width: 100%;
    padding: 10px;
    border-radius: 6px;
    border: 1px solid #ccc;
    box-sizing: border-box;
}

button {
    width: 100%;
    padding: 10px;
    border: none;
    background: #4CAF50;
    color: #fff;
    font-weight: bold;
    border-radius: 6px;
    cursor: pointer;
}

button:hover {
    background: #45a049;
}

.error {
    color: #d8000c;
    background-color: #ffbaba;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 15px;
}

.note {
    text-align: center;
    margin-top: 10px;
}
