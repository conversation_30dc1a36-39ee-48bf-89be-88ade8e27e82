% Enhanced LaTeX header for SMKTMI documentation
\usepackage{fancyhdr}
\usepackage{graphicx}
\usepackage{longtable}
\usepackage{booktabs}
\usepackage{array}
\usepackage{multirow}
\usepackage{wrapfig}
\usepackage{float}
\usepackage{colortbl}
\usepackage{pdflscape}
\usepackage{tabu}
\usepackage{threeparttable}
\usepackage{threeparttablex}
\usepackage{makecell}
\usepackage{xcolor}
\usepackage{tcolorbox}
\usepackage{listings}
\usepackage{courier}

% Page setup
\pagestyle{fancy}
\fancyhead[L]{SMKTMI System Documentation}
\fancyhead[C]{}
\fancyhead[R]{Version 1.0 - December 2024}
\fancyfoot[L]{Confidential - Internal Use Only}
\fancyfoot[C]{\thepage}
\fancyfoot[R]{System Analysis \& Design}
\renewcommand{\headrulewidth}{0.4pt}
\renewcommand{\footrulewidth}{0.4pt}

% Colors for different sections
\definecolor{headerblue}{RGB}{41, 128, 185}
\definecolor{lightblue}{RGB}{174, 214, 241}
\definecolor{darkgray}{RGB}{52, 73, 94}
\definecolor{lightgray}{RGB}{236, 240, 241}

% Table formatting
\renewcommand{\arraystretch}{1.2}

% Code block formatting
\lstset{
    basicstyle=\ttfamily\footnotesize,
    backgroundcolor=\color{lightgray},
    frame=single,
    breaklines=true,
    breakatwhitespace=true,
    showstringspaces=false,
    numbers=left,
    numberstyle=\tiny\color{darkgray},
    keywordstyle=\color{headerblue}\bfseries,
    commentstyle=\color{darkgray}\itshape,
    stringstyle=\color{red},
    tabsize=2
}

% Custom boxes for important information
\newtcolorbox{infobox}{
    colback=lightblue,
    colframe=headerblue,
    boxrule=1pt,
    arc=3pt,
    left=6pt,
    right=6pt,
    top=6pt,
    bottom=6pt
}

\newtcolorbox{warningbox}{
    colback=yellow!10,
    colframe=orange,
    boxrule=1pt,
    arc=3pt,
    left=6pt,
    right=6pt,
    top=6pt,
    bottom=6pt
}

% Section formatting
\usepackage{titlesec}
\titleformat{\section}
{\color{headerblue}\Large\bfseries}
{\thesection}{1em}{}

\titleformat{\subsection}
{\color{darkgray}\large\bfseries}
{\thesubsection}{1em}{}

\titleformat{\subsubsection}
{\color{darkgray}\normalsize\bfseries}
{\thesubsubsection}{1em}{}

% Table of contents formatting
\usepackage{tocloft}
\renewcommand{\cftsecfont}{\color{headerblue}\bfseries}
\renewcommand{\cftsubsecfont}{\color{darkgray}}

% Hyperlink setup
\usepackage{hyperref}
\hypersetup{
    colorlinks=true,
    linkcolor=headerblue,
    filecolor=headerblue,
    urlcolor=headerblue,
    citecolor=headerblue,
    pdftitle={SMKTMI School Management System Documentation},
    pdfauthor={System Analysis Team},
    pdfsubject={System Analysis and Design Documentation},
    pdfkeywords={SMKTMI, School Management, System Documentation, Database Design}
}

% Better spacing
\usepackage{setspace}
\onehalfspacing

% Prevent orphans and widows
\widowpenalty=10000
\clubpenalty=10000

% Better page breaks
\usepackage{needspace}
