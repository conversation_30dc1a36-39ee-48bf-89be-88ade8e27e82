<?php
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

require 'PHPMailer/src/Exception.php';
require 'PHPMailer/src/PHPMailer.php';
require 'PHPMailer/src/SMTP.php';

function sendVerificationEmail($to, $verify_link) {
    $mail = new PHPMailer(true);

    try {
        $mail->isSMTP();
        $mail->Host       = 'smtp.gmail.com';
        $mail->SMTPAuth   = true;
        $mail->Username   = '<EMAIL>'; 
        $mail->Password   = 'csop krln zhae vqxq';   
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port       = 587;

        $mail->setFrom('<EMAIL>', 'Admin Sistem SMKTMI');
        $mail->addAddress($to);
        $mail->isHTML(true);
        $mail->Subject = 'Pengesahan Emel Akaun';
        $mail->Body    = "Sila klik pautan ini untuk sahkan emel anda:<br><a href='$verify_link'>$verify_link</a>";
        $mail->AltBody = "Klik link untuk sahkan emel: $verify_link";

        $mail->send();
    } catch (Exception $e) {
        echo "Emel gagal dihantar: {$mail->ErrorInfo}";
    }
}
?>
