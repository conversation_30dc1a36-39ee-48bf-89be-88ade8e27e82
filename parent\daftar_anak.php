<?php
session_start();

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Parent') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';

$parent_user_id = $_SESSION['user_id'];
$stmt = $conn->prepare("SELECT parent_id FROM parents WHERE user_id = ?");
$stmt->bind_param("i", $parent_user_id);
$stmt->execute();
$result = $stmt->get_result();
$parent = $result->fetch_assoc();

if (!$parent) {
    die("Maklumat ibu bapa tidak dijumpai. Sila hubungi pentadbir sistem.");
}

$parent_id = $parent['parent_id'] ?? null;

$success = "";
$error = "";

function is_valid_password($password) {
    return preg_match('/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/', $password);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = $_POST['full_name'];
    $no_ic = $_POST['no_ic'];
    $gender = $_POST['gender'];
    $birth_date = $_POST['birth_date'];
    $relation = $_POST['relation'];
    $student_email = $_POST['student_email'];
    $student_password = $_POST['student_password'];

    // Validate No IC (must be digits only)
    if (!preg_match('/^\d{12}$/', $no_ic)) {
        $error = "No Kad Pengenalan mestilah 12 digit nombor tanpa tanda sengkang.";
    }
    // Validate password strength
    elseif (!is_valid_password($student_password)) {
        $error = "Kata Laluan Anak mesti mempunyai sekurang-kurangnya 8 aksara termasuk huruf besar, huruf kecil, nombor dan simbol khas.";
    }
    else {
        // Calculate age (year only)
        $birth_year = date('Y', strtotime($birth_date));
        $current_year = date('Y');
        $age = $current_year - $birth_year;

        $password_hash = password_hash($student_password, PASSWORD_DEFAULT);
        $role_id = 4;

        $stmtCheck = $conn->prepare("SELECT * FROM users WHERE email = ?");
        $stmtCheck->bind_param("s", $student_email);
        $stmtCheck->execute();
        $existingUser = $stmtCheck->get_result()->fetch_assoc();

        if ($existingUser) {
            $error = "Emel ini telah digunakan. Sila guna email lain.";
        } else {
            $stmtUser = $conn->prepare("INSERT INTO users (email, password_hash, role_id) VALUES (?, ?, ?)");
            $stmtUser->bind_param("ssi", $student_email, $password_hash, $role_id);
            $stmtUser->execute();

            $new_user_id = $stmtUser->insert_id;

            $stmtStudent = $conn->prepare("INSERT INTO students (user_id, full_name, no_ic, gender, birth_date, parent_id, relation, age) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
            $stmtStudent->bind_param("issssssi", $new_user_id, $full_name, $no_ic, $gender, $birth_date, $parent_id, $relation, $age);

            if ($stmtStudent->execute()) {
                $success = "Anak berjaya didaftarkan.";
            } else {
                $error = "Ralat semasa menyimpan data anak.";
            }
        }
    }
}

// Dapatkan senarai anak
$children = [];
if ($parent_id) {
    $stmtChildren = $conn->prepare("SELECT full_name, no_ic, gender, birth_date, relation, age FROM students WHERE parent_id = ?");
    $stmtChildren->bind_param("i", $parent_id);
    $stmtChildren->execute();
    $children = $stmtChildren->get_result();
}
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pendaftaran Anak - SMKTMI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>

<style>
/* Content Styling */
.content {
    margin-left: 280px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.form-container {
    width: 100%;
    max-width: 1200px;
    background-color: #fff;
    padding: 30px 40px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0 auto;
}

.form-container h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 25px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    color: #2980b9;
    text-align: center;
}

.alert {
    padding: 15px;
    background-color: #d9edf7;
    border: 1px solid #bce8f1;
    border-radius: 5px;
    color: #31708f;
    margin-bottom: 25px;
    text-align: center;
}

.alert-success {
    background-color: #dff0d8;
    border-color: #d6e9c6;
    color: #3c763d;
}

.alert-danger {
    background-color: #f2dede;
    border-color: #ebccd1;
    color: #a94442;
}

.stats-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-left: 4px solid #3498db;
    transition: transform 0.2s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-primary {
    background-color: #3498db;
    border-color: #3498db;
    padding: 10px 30px;
    font-weight: 600;
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

/* Responsive */
@media (max-width: 768px) {
    .content {
        margin-left: 0;
        padding: 20px;
    }
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
  <div class="form-container">
    <h2><i class="fas fa-user-plus"></i> Pendaftaran Anak</h2>

    <?php if (!empty($success)): ?>
      <div class="alert alert-success">
        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success); ?>
      </div>
    <?php elseif (!empty($error)): ?>
      <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
      </div>
    <?php endif; ?>

    <div class="row">
      <div class="col-md-5">
        <div class="stats-card">
          <h5 style="margin-bottom: 20px; color: #2c3e50; text-align: center;">
            <i class="fas fa-child"></i> Borang Pendaftaran Anak
          </h5>

          <form method="post" autocomplete="off">
            <div class="mb-3">
              <label class="form-label"><strong>Nama Penuh Anak:</strong></label>
              <input type="text" name="full_name" class="form-control" required>
            </div>

            <div class="mb-3">
              <label class="form-label"><strong>No Kad Pengenalan:</strong></label>
              <input type="text" name="no_ic" class="form-control" required pattern="\d{12}"
                     title="Masukkan 12 digit nombor tanpa tanda sengkang" placeholder="123456789012">
            </div>

            <div class="mb-3">
              <label class="form-label"><strong>Jantina:</strong></label>
              <select name="gender" class="form-select" required>
                <option value="">-- Pilih Jantina --</option>
                <option value="Lelaki">Lelaki</option>
                <option value="Perempuan">Perempuan</option>
              </select>
            </div>

            <div class="mb-3">
              <label class="form-label"><strong>Tarikh Lahir:</strong></label>
              <input type="date" name="birth_date" class="form-control" required>
            </div>

            <div class="mb-3">
              <label class="form-label"><strong>Hubungan:</strong></label>
              <select name="relation" class="form-select" required>
                <option value="">-- Pilih Hubungan --</option>
                <option value="Ibu">Ibu</option>
                <option value="Bapa">Bapa</option>
                <option value="Penjaga">Penjaga</option>
              </select>
            </div>

            <div class="mb-3">
              <label class="form-label"><strong>Emel Anak:</strong></label>
              <input type="email" name="student_email" class="form-control" required autocomplete="off"
                     placeholder="<EMAIL>">
            </div>

            <div class="mb-3">
              <label class="form-label"><strong>Kata Laluan Anak:</strong></label>
              <div class="input-group">
                <input type="password" name="student_password" id="student_password" class="form-control" required
                       pattern="(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&]).{8,}"
                       title="Minimum 8 aksara termasuk huruf besar, huruf kecil, nombor dan simbol khas"
                       autocomplete="new-password">
                <button class="btn btn-outline-secondary" type="button" onclick="toggleStudentPassword()">
                  <i class="fas fa-eye" id="toggleStudentPasswordIcon"></i>
                </button>
              </div>
              <small class="form-text text-muted">
                Kata laluan mesti mengandungi sekurang-kurangnya 8 aksara dengan huruf besar, huruf kecil, nombor dan simbol khas.
              </small>
            </div>

            <div class="text-center">
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-user-plus"></i> Daftar Anak
              </button>
            </div>
          </form>
        </div>
      </div>

      <div class="col-md-7">
        <div class="stats-card">
          <h5 style="margin-bottom: 20px; color: #2c3e50; text-align: center;">
            <i class="fas fa-list"></i> Anak Yang Telah Didaftar
          </h5>

          <?php if ($children && $children->num_rows > 0): ?>
            <table class="table table-bordered">
              <thead class="table-light">
                <tr>
                  <th>Nama</th>
                  <th>No IC</th>
                  <th>Jantina</th>
                  <th>Tarikh Lahir</th>
                  <th>Hubungan</th>
                  <th>Umur</th>
                </tr>
              </thead>
              <tbody>
                <?php while ($child = $children->fetch_assoc()): ?>
                  <tr>
                    <td><?php echo htmlspecialchars($child['full_name']); ?></td>
                    <td><?php echo htmlspecialchars($child['no_ic']); ?></td>
                    <td><?php echo htmlspecialchars($child['gender']); ?></td>
                    <td><?php echo date('d/m/Y', strtotime($child['birth_date'])); ?></td>
                    <td><?php echo htmlspecialchars($child['relation']); ?></td>
                    <td><?php echo htmlspecialchars($child['age']); ?> Tahun</td>
                  </tr>
                <?php endwhile; ?>
              </tbody>
            </table>
          <?php else: ?>
            <div class="text-center" style="padding: 40px;">
              <i class="fas fa-user-plus" style="font-size: 48px; color: #bdc3c7; margin-bottom: 15px;"></i>
              <h5 style="color: #7f8c8d;">Belum Ada Anak Didaftarkan</h5>
              <p style="color: #95a5a6;">Sila gunakan borang di sebelah untuk mendaftarkan anak anda.</p>
            </div>
          <?php endif; ?>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function toggleStudentPassword() {
    const passwordField = document.getElementById('student_password');
    const toggleIcon = document.getElementById('toggleStudentPasswordIcon');

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}
</script>

</body>
</html>
