<?php
include 'includes/header.php';
require '../db.php';

$message = "";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $classroom_id = intval($_POST['classroom_id']);
    $teacher_id = intval($_POST['teacher_id']);

    // Update classroom with teacher_id
    $stmt = $conn->prepare("UPDATE classrooms SET teacher_id = ? WHERE classroom_id = ?");
    if (!$stmt) {
        die("Prepare failed: " . $conn->error);
    }

    $stmt->bind_param("ii", $teacher_id, $classroom_id);
    if ($stmt->execute()) {
        // Redirect to add_classroom.php with success
        header("Location: add_classroom.php?success=1");
        exit;
    } else {
        $message = "❌ Error: " . $stmt->error;
    }
}

// Fetch classrooms without assigned teacher
$classrooms_result = $conn->query("SELECT classroom_id, class_name FROM classrooms WHERE teacher_id IS NULL");

// Fetch teachers not assigned to any classroom
$teachers_result = $conn->query("SELECT t.teacher_id, t.full_name FROM teachers t
    WHERE t.teacher_id NOT IN (SELECT teacher_id FROM classrooms WHERE teacher_id IS NOT NULL)");
?>

<!-- FontAwesome CDN for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<style>
/* Sidebar Styling */
.sidebar {
    position: fixed;
    top: 0; left: 0;
    width: 300px;
    height: 100vh;
    background-color: #2c3e50;
    padding: 30px 20px;
    color: #ecf0f1;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow-y: auto;
    box-shadow: 5px 0 15px rgba(0,0,0,0.4);
    border-right: 1px solid #34495e;
}

.sidebar h4 {
    font-size: 18px;
    font-weight: 700;
    text-transform: uppercase;
    margin: 30px 0 12px;
    border-bottom: 2px solid #3498db;
    padding-bottom: 6px;
    letter-spacing: 1.5px;
}

.sidebar a {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    color: #bdc3c7;
    text-decoration: none;
    font-size: 16px;
    margin-bottom: 8px;
    border-radius: 6px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.sidebar a i {
    margin-right: 12px;
    width: 18px;
    text-align: center;
}

.sidebar a:hover {
    background-color: #2980b9;
    color: #ecf0f1;
}

/* Content Styling */
.content {
    margin-left: 280px;
    padding: 40px 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.form-container {
    width: 100%;
    max-width: 700px;
    background-color: #fff;
    padding: 30px 40px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.form-container h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 25px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    color: #2980b9;
    text-align: center;
}

.alert {
    padding: 15px;
    background-color: #f2dede;
    border: 1px solid #ebccd1;
    border-radius: 5px;
    color: #a94442;
    margin-bottom: 25px;
    text-align: center;
}

/* Form Styling */
form {
    display: flex;
    flex-direction: column;
    gap: 18px;
}

label {
    font-weight: 700;
    margin-bottom: 6px;
    display: block;
    color: #34495e;
}

select {
    width: 100%;
    padding: 10px 14px;
    font-size: 16px;
    border: 1.5px solid #ccc;
    border-radius: 6px;
    transition: border-color 0.3s ease;
}

select:focus {
    border-color: #2980b9;
    outline: none;
}

button[type="submit"] {
    width: 150px;
    align-self: center;
    padding: 12px 0;
    background-color: #2980b9;
    border: none;
    color: #ecf0f1;
    font-size: 18px;
    font-weight: 700;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-top: 10px;
}

button[type="submit"]:hover {
    background-color: #1a5d8f;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
        box-shadow: none;
    }
    .content {
        margin-left: 0;
        padding: 20px;
    }
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
    <div class="form-container">
        <h2>Tetapan Guru Kelas</h2>

        <?php if ($message): ?>
            <div class="alert"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>

        <form method="post">
            <label for="classroom_id">Pilih Kelas</label>
            <select name="classroom_id" id="classroom_id" required>
                <option value="" disabled selected>-- Pilih Kelas --</option>
                <?php while ($row = $classrooms_result->fetch_assoc()): ?>
                    <option value="<?php echo $row['classroom_id']; ?>">
                        <?php echo htmlspecialchars($row['class_name']); ?>
                    </option>
                <?php endwhile; ?>
            </select>

            <label for="teacher_id">Pilih Guru</label>
            <select name="teacher_id" id="teacher_id" required>
                <option value="" disabled selected>-- Pilih Guru --</option>
                <?php while ($row = $teachers_result->fetch_assoc()): ?>
                    <option value="<?php echo $row['teacher_id']; ?>">
                        <?php echo htmlspecialchars($row['full_name']); ?>
                    </option>
                <?php endwhile; ?>
            </select>

            <button type="submit">Tetapkan</button>
        </form>
    </div>
</div>
</body>
</html>
