<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header("Location: ../login.php");
    exit;
}

// Include database connection
require '../db.php';
require '../includes/universal_logger.php';

$message = "";
$message_type = "";

// Handle delete request
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $teacher_id = (int)$_GET['delete'];

    // Check if teacher has any assignments or dependencies
    $check_assignments = $conn->prepare("SELECT COUNT(*) as count FROM teacher_subject_classrooms WHERE teacher_id = ?");
    $check_assignments->bind_param("i", $teacher_id);
    $check_assignments->execute();
    $assignment_result = $check_assignments->get_result();
    $assignment_count = $assignment_result->fetch_assoc()['count'];
    $check_assignments->close();

    $check_classrooms = $conn->prepare("SELECT COUNT(*) as count FROM classrooms WHERE teacher_id = ?");
    $check_classrooms->bind_param("i", $teacher_id);
    $check_classrooms->execute();
    $classroom_result = $check_classrooms->get_result();
    $classroom_count = $classroom_result->fetch_assoc()['count'];
    $check_classrooms->close();

    if ($assignment_count > 0 || $classroom_count > 0) {
        $message = "❌ Tidak boleh padam guru ini kerana masih mempunyai tugasan mengajar atau kelas yang ditetapkan.";
        $message_type = "error";
    } else {
        // Get teacher info for logging
        $teacher_stmt = $conn->prepare("SELECT full_name, staff_id FROM teachers WHERE teacher_id = ?");
        $teacher_stmt->bind_param("i", $teacher_id);
        $teacher_stmt->execute();
        $teacher_result = $teacher_stmt->get_result();
        $teacher_info = $teacher_result->fetch_assoc();
        $teacher_stmt->close();

        if ($teacher_info) {
            // Get user_id for this teacher
            $user_stmt = $conn->prepare("SELECT user_id FROM teachers WHERE teacher_id = ?");
            $user_stmt->bind_param("i", $teacher_id);
            $user_stmt->execute();
            $user_result = $user_stmt->get_result();
            $user_data = $user_result->fetch_assoc();
            $user_stmt->close();

            // Delete teacher record
            $delete_teacher = $conn->prepare("DELETE FROM teachers WHERE teacher_id = ?");
            $delete_teacher->bind_param("i", $teacher_id);

            if ($delete_teacher->execute()) {
                // Also delete the user account
                if ($user_data && $user_data['user_id']) {
                    $delete_user = $conn->prepare("DELETE FROM users WHERE user_id = ?");
                    $delete_user->bind_param("i", $user_data['user_id']);
                    $delete_user->execute();
                    $delete_user->close();
                }

                // Log the deletion
                logAdminActivity($conn, 'DELETE', 'TEACHER', $teacher_id, $teacher_info['full_name'],
                    "Guru {$teacher_info['full_name']} (ID: {$teacher_info['staff_id']}) telah dipadam");

                $message = "✅ Guru " . htmlspecialchars($teacher_info['full_name']) . " (ID: " . htmlspecialchars($teacher_info['staff_id']) . ") berjaya dipadam.";
                $message_type = "success";
            } else {
                $message = "❌ Ralat semasa memadam guru: " . $delete_teacher->error;
                $message_type = "error";
            }
            $delete_teacher->close();
        } else {
            $message = "❌ Guru tidak dijumpai.";
            $message_type = "error";
        }
    }
}

// Pagination settings
$records_per_page = 10;
$current_page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($current_page - 1) * $records_per_page;

// Search functionality
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$search_condition = '';
$search_params = [];

if (!empty($search)) {
    $search_condition = "WHERE full_name LIKE ? OR staff_id LIKE ? OR gender LIKE ? OR phone_number LIKE ?";
    $search_term = "%$search%";
    $search_params = [$search_term, $search_term, $search_term, $search_term];
}

// Count total records for pagination
$count_query = "SELECT COUNT(*) as total FROM teachers $search_condition";
if (!empty($search_params)) {
    $count_stmt = $conn->prepare($count_query);
    $count_stmt->bind_param("ssss", ...$search_params);
    $count_stmt->execute();
    $count_result = $count_stmt->get_result();
} else {
    $count_result = $conn->query($count_query);
}
$total_records = $count_result->fetch_assoc()['total'];
$total_pages = ceil($total_records / $records_per_page);

// Fetch teachers with pagination and search
$teachers_query = "SELECT teacher_id, full_name, staff_id, phone_number, gender FROM teachers $search_condition ORDER BY full_name LIMIT $records_per_page OFFSET $offset";
if (!empty($search_params)) {
    $teachers_stmt = $conn->prepare($teachers_query);
    $teachers_stmt->bind_param("ssss", ...$search_params);
    $teachers_stmt->execute();
    $teachers_result = $teachers_stmt->get_result();
} else {
    $teachers_result = $conn->query($teachers_query);
}

include 'includes/header.php';
?>

<!-- FontAwesome CDN for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<style>
/* Sidebar Styling */
.sidebar {
    position: fixed;
    top: 0; left: 0;
    width: 300px;
    height: 100vh;
    background-color: #2c3e50;
    padding: 30px 20px;
    color: #ecf0f1;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow-y: auto;
    box-shadow: 5px 0 15px rgba(0,0,0,0.4);
    border-right: 1px solid #34495e;
}

.sidebar h4 {
    font-size: 18px;
    font-weight: 700;
    text-transform: uppercase;
    margin: 30px 0 12px;
    border-bottom: 2px solid #3498db;
    padding-bottom: 6px;
    letter-spacing: 1.5px;
}

.sidebar a {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    color: #bdc3c7;
    text-decoration: none;
    font-size: 16px;
    margin-bottom: 8px;
    border-radius: 6px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.sidebar a i {
    margin-right: 12px;
    width: 18px;
    text-align: center;
}

.sidebar a:hover {
    background-color: #2980b9;
    color: #ecf0f1;
}

/* Content Styling */
.content {
    margin-left: 280px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.list-container {
    width: 100%;
    max-width: 1200px;
    background-color: #fff;
    padding: 30px 40px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0 auto;
}

.list-container h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 25px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    color: #2980b9;
    text-align: center;
}

.table {
    margin-top: 20px;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: flex-start;
}

.btn-edit {
    background-color: #3498db;
    color: white;
    padding: 6px 12px;
    text-decoration: none;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s ease;
}

.btn-edit:hover {
    background-color: #2980b9;
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.btn-delete {
    background-color: #e74c3c;
    color: white;
    padding: 6px 12px;
    text-decoration: none;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s ease;
}

.btn-delete:hover {
    background-color: #c0392b;
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
    font-weight: 600;
}

.alert-success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.alert-error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.no-teachers {
    text-align: center;
    color: #7f8c8d;
    font-style: italic;
    padding: 40px;
}
    .search-form {
        margin-bottom: 20px;
        display: flex;
        gap: 10px;
        align-items: center;
    }
    .search-input {
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        width: 300px;
    }
    .search-btn {
        padding: 8px 16px;
        background-color: #3498db;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        text-decoration: none;
        font-size: 14px;
    }
    .search-btn:hover {
        background-color: #2980b9;
    }
    .clear-btn {
        padding: 8px 16px;
        background-color: #95a5a6;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        text-decoration: none;
        font-size: 14px;
    }
    .clear-btn:hover {
        background-color: #7f8c8d;
        text-decoration: none;
        color: white;
    }
    .pagination {
        margin-top: 20px;
        display: flex;
        justify-content: center;
        gap: 5px;
        align-items: center;
    }
    .pagination a, .pagination span {
        padding: 8px 12px;
        border: 1px solid #ddd;
        text-decoration: none;
        color: #3498db;
        border-radius: 4px;
    }
    .pagination a:hover {
        background-color: #3498db;
        color: white;
    }
    .pagination .current {
        background-color: #3498db;
        color: white;
        border-color: #3498db;
    }
    .pagination .disabled {
        color: #bdc3c7;
        cursor: not-allowed;
    }
    .pagination .disabled:hover {
        background-color: transparent;
        color: #bdc3c7;
    }

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
        box-shadow: none;
    }
    .content {
        margin-left: 0;
        padding: 20px;
    }
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
    <div class="list-container">
        <h2>Senarai Guru - Kemaskini Maklumat</h2>

        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $message_type; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

    <!-- Search Form -->
    <form method="GET" action="" class="search-form">
        <input type="text" name="search" class="search-input" placeholder="Cari nama, ID staf, jantina atau telefon..." value="<?php echo htmlspecialchars($search); ?>">
        <button type="submit" class="search-btn">
            <i class="fas fa-search"></i> Cari
        </button>
        <?php if (!empty($search)): ?>
            <a href="list_teachers.php" class="clear-btn">
                <i class="fas fa-times"></i> Padam
            </a>
        <?php endif; ?>
    </form>

    <?php if (!empty($search)): ?>
        <div style="margin-bottom: 15px; color: #7f8c8d;">
            <small>Menunjukkan hasil carian untuk: "<strong><?php echo htmlspecialchars($search); ?></strong>" (<?php echo $total_records; ?> rekod dijumpai)</small>
        </div>
    <?php endif; ?>

    <?php if ($teachers_result && $teachers_result->num_rows > 0): ?>
        <table class="table table-bordered table-striped">
            <thead class="table-dark">
                <tr>
                    <th style="width: 50px;">Bil.</th>
                    <th>Nama Penuh</th>
                    <th>ID Staf</th>
                    <th>Jantina</th>
                    <th>No. Telefon</th>
                    <th style="width: 160px;">Tindakan</th>
                </tr>
            </thead>
            <tbody>
                <?php
                $bil = $offset + 1;
                while ($teacher = $teachers_result->fetch_assoc()):
                ?>
                    <tr>
                        <td><?php echo $bil++; ?></td>
                        <td><?php echo htmlspecialchars($teacher['full_name']); ?></td>
                        <td><?php echo htmlspecialchars($teacher['staff_id']); ?></td>
                        <td><?php echo htmlspecialchars($teacher['gender'] ?? '-'); ?></td>
                        <td><?php echo htmlspecialchars($teacher['phone_number'] ?? '-'); ?></td>
                        <td>
                            <div class="action-buttons">
                                <a href="update_teacher.php?id=<?php echo $teacher['teacher_id']; ?>" class="btn-edit">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                                <button onclick="confirmDelete(<?php echo $teacher['teacher_id']; ?>, '<?php echo htmlspecialchars($teacher['full_name'], ENT_QUOTES); ?>')" class="btn-delete">
                                    <i class="fas fa-trash"></i> Padam
                                </button>
                            </div>
                        </td>
                    </tr>
                <?php endwhile; ?>
            </tbody>
        </table>

        <div style="margin-top: 20px; color: #7f8c8d;">
            <p><strong>Jumlah Guru:</strong> <?php echo $total_records; ?> orang
            <?php if ($total_pages > 1): ?>
                | Halaman <?php echo $current_page; ?> daripada <?php echo $total_pages; ?>
            <?php endif; ?>
            </p>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <div class="pagination">
                <?php
                // Build query string for pagination links
                $query_params = [];
                if (!empty($search)) {
                    $query_params['search'] = $search;
                }
                $query_string = !empty($query_params) ? '&' . http_build_query($query_params) : '';

                // Previous button
                if ($current_page > 1): ?>
                    <a href="?page=<?php echo ($current_page - 1) . $query_string; ?>">&laquo; Sebelum</a>
                <?php else: ?>
                    <span class="disabled">&laquo; Sebelum</span>
                <?php endif; ?>

                <?php
                // Page numbers
                $start_page = max(1, $current_page - 2);
                $end_page = min($total_pages, $current_page + 2);

                if ($start_page > 1): ?>
                    <a href="?page=1<?php echo $query_string; ?>">1</a>
                    <?php if ($start_page > 2): ?>
                        <span>...</span>
                    <?php endif; ?>
                <?php endif; ?>

                <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                    <?php if ($i == $current_page): ?>
                        <span class="current"><?php echo $i; ?></span>
                    <?php else: ?>
                        <a href="?page=<?php echo $i . $query_string; ?>"><?php echo $i; ?></a>
                    <?php endif; ?>
                <?php endfor; ?>

                <?php if ($end_page < $total_pages): ?>
                    <?php if ($end_page < $total_pages - 1): ?>
                        <span>...</span>
                    <?php endif; ?>
                    <a href="?page=<?php echo $total_pages . $query_string; ?>"><?php echo $total_pages; ?></a>
                <?php endif; ?>

                <!-- Next button -->
                <?php if ($current_page < $total_pages): ?>
                    <a href="?page=<?php echo ($current_page + 1) . $query_string; ?>">Seterus &raquo;</a>
                <?php else: ?>
                    <span class="disabled">Seterus &raquo;</span>
                <?php endif; ?>
            </div>
        <?php endif; ?>

    <?php else: ?>
        <div class="no-teachers">
            <i class="fas fa-users" style="font-size: 48px; color: #bdc3c7; margin-bottom: 20px;"></i>
            <h4>Tiada Guru Didaftarkan</h4>
            <p>Belum ada guru yang didaftarkan dalam sistem.</p>
            <a href="register_teacher.php" class="btn btn-primary">
                <i class="fas fa-user-plus"></i> Daftar Guru Baru
            </a>
        </div>
    <?php endif; ?>
    </div>
</div>

<script>
function confirmDelete(teacherId, teacherName) {
    if (confirm('Adakah anda pasti ingin memadam guru "' + teacherName + '"?\n\nTindakan ini tidak boleh dibatalkan.')) {
        window.location.href = 'list_teachers.php?delete=' + teacherId;
    }
}
</script>

</body>
</html>
