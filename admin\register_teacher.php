<?php
include 'includes/header.php';
require '../db.php';
require '../includes/universal_logger.php';

$message = "";

// Function to validate password strength
function is_valid_password($password) {
    return preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$/', $password);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email']);
    $raw_password = $_POST['password'];
    $full_name = trim($_POST['full_name']);
    $staff_id = trim($_POST['staff_id']);
    $phone_number = trim($_POST['phone_number']);
    $gender = trim($_POST['gender']);

    if (!empty($email) && !empty($raw_password)) {
        // Enforce password policy
        if (!is_valid_password($raw_password)) {
            $message = "❌ Kata laluan mesti mempunyai sekurang-kurangnya 8 aksara, 1 huruf besar, 1 huruf kecil, 1 nombor dan 1 simbol khas.";
        } else {
            $hashed_password = password_hash($raw_password, PASSWORD_DEFAULT);

            // Insert into users table
            $stmt = $conn->prepare("INSERT INTO users (email, password_hash, role_id) VALUES (?, ?, 3)");
            if ($stmt) {
                $stmt->bind_param("ss", $email, $hashed_password);
                if ($stmt->execute()) {
                    $user_id = $stmt->insert_id;

                    // Insert into teachers table
                    $stmt2 = $conn->prepare("INSERT INTO teachers (user_id, full_name, staff_id, phone_number, gender) VALUES (?, ?, ?, ?, ?)");
                    if ($stmt2) {
                        $stmt2->bind_param("issss", $user_id, $full_name, $staff_id, $phone_number, $gender);
                        if ($stmt2->execute()) {
                            // Log the teacher registration with details
                            $details = [
                                'Nama Penuh' => $full_name,
                                'ID Staf' => $staff_id,
                                'Email' => $email,
                                'Jantina' => $gender,
                                'Nombor Telefon' => $phone_number
                            ];

                            logAdminActivity($conn, 'CREATE', 'TEACHER', $conn->insert_id, $full_name,
                                "Guru baru didaftarkan", $details);

                            $message = "✅ Pendaftaran guru berjaya.";
                        } else {
                            $message = "❌ Ralat (guru): " . $stmt2->error;
                        }
                        $stmt2->close();
                    } else {
                        $message = "❌ Ralat penyediaan (guru): " . $conn->error;
                    }
                } else {
                    $message = "❌ Ralat (pengguna): " . $stmt->error;
                }
                $stmt->close();
            } else {
                $message = "❌ Ralat penyediaan (pengguna): " . $conn->error;
            }
        }
    } else {
        $message = "❌ Sila isi semua maklumat yang diperlukan.";
    }
}
?>

<!-- FontAwesome CDN for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<style>
/* Sidebar Styling */
.sidebar {
    position: fixed;
    top: 0; left: 0;
    width: 300px;
    height: 100vh;
    background-color: #2c3e50;
    padding: 30px 20px;
    color: #ecf0f1;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow-y: auto;
    box-shadow: 5px 0 15px rgba(0,0,0,0.4);
    border-right: 1px solid #34495e;
}

.sidebar h4 {
    font-size: 18px;
    font-weight: 700;
    text-transform: uppercase;
    margin: 30px 0 12px;
    border-bottom: 2px solid #3498db;
    padding-bottom: 6px;
    letter-spacing: 1.5px;
}

.sidebar a {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    color: #bdc3c7;
    text-decoration: none;
    font-size: 16px;
    margin-bottom: 8px;
    border-radius: 6px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.sidebar a i {
    margin-right: 12px;
    width: 18px;
    text-align: center;
}

.sidebar a:hover {
    background-color: #2980b9;
    color: #ecf0f1;
}

/* Content Styling */
.content {
    margin-left: 280px;
    padding: 40px 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.form-container {
    width: 100%;
    max-width: 700px;
    background-color: #fff;
    padding: 30px 40px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.form-container h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 25px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    color: #2980b9;
    text-align: center;
}

.alert {
    padding: 15px;
    background-color: #d9edf7;
    border: 1px solid #bce8f1;
    border-radius: 5px;
    color: #31708f;
    margin-bottom: 25px;
    text-align: center;
}

/* Form Styling */
form {
    display: flex;
    flex-direction: column;
    gap: 18px;
}

label {
    font-weight: 700;
    margin-bottom: 6px;
    display: block;
    color: #34495e;
}

input[type="email"],
input[type="password"],
input[type="text"],
select {
    width: 100%;
    padding: 10px 14px;
    font-size: 16px;
    border: 1.5px solid #ccc;
    border-radius: 6px;
    transition: border-color 0.3s ease;
}

/* Password Container Styling */
.password-container {
    position: relative;
    display: flex;
    align-items: center;
}

.password-container input[type="password"],
.password-container input[type="text"] {
    padding-right: 45px;
}

.password-toggle {
    position: absolute;
    right: 10px;
    background: none;
    border: none;
    cursor: pointer;
    color: #7f8c8d;
    font-size: 16px;
    padding: 5px;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #2980b9;
}

.password-toggle:focus {
    outline: none;
}

input:focus,
select:focus {
    border-color: #2980b9;
    outline: none;
}

button[type="submit"] {
    width: 150px;
    align-self: center;
    padding: 12px 0;
    background-color: #2980b9;
    border: none;
    color: #ecf0f1;
    font-size: 18px;
    font-weight: 700;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

button[type="submit"]:hover {
    background-color: #1a5d8f;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
        box-shadow: none;
    }
    .content {
        margin-left: 0;
        padding: 20px;
    }
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
    <div class="form-container">
        <h2>Pendaftaran Guru</h2>

        <?php if ($message): ?>
            <div class="alert"><?= htmlspecialchars($message) ?></div>
        <?php endif; ?>

        <form method="post" autocomplete="off" novalidate>
            <label for="email">Emel</label>
            <input type="email" id="email" name="email" required>

            <label for="password">Kata laluan</label>
            <div class="password-container">
                <input type="password" id="password" name="password" autocomplete="new-password" required>
                <button type="button" class="password-toggle" onclick="togglePassword()">
                    <i class="fas fa-eye" id="toggleIcon"></i>
                </button>
            </div>

            <label for="full_name">Nama Penuh</label>
            <input type="text" id="full_name" name="full_name" required>

            <label for="staff_id">ID Staf</label>
            <input type="text" id="staff_id" name="staff_id" required>

            <label for="phone_number">Nombor Telefon</label>
            <input type="text" id="phone_number" name="phone_number" required>

            <label for="gender">Jantina</label>
            <select id="gender" name="gender" required>
                <option value="" disabled selected>Pilih Jantina</option>
                <option value="Lelaki">Lelaki</option>
                <option value="Perempuan">Perempuan</option>
            </select>

            <button type="submit">Hantar</button>
        </form>
    </div>
</div>

<script>
function togglePassword() {
    const passwordField = document.getElementById('password');
    const toggleIcon = document.getElementById('toggleIcon');

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}
</script>

</body>
</html>
