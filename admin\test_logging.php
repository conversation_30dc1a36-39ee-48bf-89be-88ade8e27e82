<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';
require '../includes/universal_logger.php';

$message = "";

// Test logging functionality
if (isset($_POST['test_log'])) {
    $test_result = logUserActivity($conn, 'TEST', 'SYSTEM', null, 'Test Target', 'Testing logging functionality');
    
    if ($test_result) {
        $message = "✅ Test log entry created successfully!";
    } else {
        $message = "❌ Failed to create test log entry.";
    }
}

// Get recent logs for verification
$recent_logs_query = "SELECT * FROM admin_logs ORDER BY created_at DESC LIMIT 10";
$recent_logs_result = $conn->query($recent_logs_query);

include 'includes/header.php';
?>

<style>
.content {
    margin-left: 280px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.test-container {
    width: 100%;
    max-width: 1000px;
    background-color: #fff;
    padding: 30px 40px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0 auto;
}

.test-container h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 25px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    color: #2980b9;
    text-align: center;
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
    font-weight: 600;
}

.alert-success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.alert-error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    margin-right: 10px;
    font-size: 14px;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.session-info {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 6px;
    margin-bottom: 25px;
    border-left: 4px solid #3498db;
}

.logs-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.logs-table th,
.logs-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
    font-size: 12px;
}

.logs-table th {
    background-color: rgb(153, 156, 158);
    color: white;
    font-weight: 600;
}

.logs-table tr:hover {
    background-color: #f8f9fa;
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
    <div class="test-container">
        <h2><i class="fas fa-bug"></i> Test Logging System</h2>
        
        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo strpos($message, '✅') !== false ? 'success' : 'error'; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <!-- Session Information -->
        <div class="session-info">
            <h4>Current Session Information:</h4>
            <ul>
                <li><strong>User ID:</strong> <?php echo $_SESSION['user_id'] ?? 'Not set'; ?></li>
                <li><strong>Role:</strong> <?php echo $_SESSION['role'] ?? 'Not set'; ?></li>
                <li><strong>Name:</strong> <?php echo $_SESSION['name'] ?? 'Not set'; ?></li>
                <li><strong>Admin ID:</strong> <?php echo $_SESSION['admin_id'] ?? 'Not set'; ?></li>
                <li><strong>Teacher ID:</strong> <?php echo $_SESSION['teacher_id'] ?? 'Not set'; ?></li>
                <li><strong>Student ID:</strong> <?php echo $_SESSION['student_id'] ?? 'Not set'; ?></li>
                <li><strong>Parent ID:</strong> <?php echo $_SESSION['parent_id'] ?? 'Not set'; ?></li>
            </ul>
        </div>
        
        <!-- Test Logging -->
        <div style="text-align: center; margin: 30px 0;">
            <form method="POST">
                <button type="submit" name="test_log" class="btn btn-primary">
                    <i class="fas fa-play"></i> Test Logging Function
                </button>
            </form>
        </div>
        
        <!-- Recent Logs -->
        <h4>Recent Log Entries (Last 10):</h4>
        <?php if ($recent_logs_result && $recent_logs_result->num_rows > 0): ?>
            <table class="logs-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Time</th>
                        <th>User ID</th>
                        <th>User Name</th>
                        <th>Action</th>
                        <th>Target Type</th>
                        <th>Target Name</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <?php while ($log = $recent_logs_result->fetch_assoc()): ?>
                        <tr>
                            <td><?php echo $log['log_id']; ?></td>
                            <td><?php echo date('d/m/Y H:i:s', strtotime($log['created_at'])); ?></td>
                            <td><?php echo $log['admin_id']; ?></td>
                            <td><?php echo htmlspecialchars($log['admin_name'] ?? 'Empty'); ?></td>
                            <td><?php echo htmlspecialchars($log['action']); ?></td>
                            <td><?php echo htmlspecialchars($log['target_type']); ?></td>
                            <td><?php echo htmlspecialchars($log['target_name'] ?? 'N/A'); ?></td>
                            <td><?php echo htmlspecialchars($log['description'] ?? 'Empty'); ?></td>
                        </tr>
                    <?php endwhile; ?>
                </tbody>
            </table>
        <?php else: ?>
            <p>No log entries found.</p>
        <?php endif; ?>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="view_admin_logs.php" class="btn btn-primary">
                <i class="fas fa-list"></i> View Full Logs
            </a>
            <a href="dashboard.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>
</div>

</body>
</html>
