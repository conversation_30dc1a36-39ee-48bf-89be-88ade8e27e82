<?php
session_start();

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Teacher') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';

$teacherId = $_SESSION['teacher_id'];

// Get teacher's classrooms through both teacher_subject_classrooms AND direct class teacher assignment
$classroomStmt = $conn->prepare("
    SELECT DISTINCT c.classroom_id, c.class_name
    FROM classrooms c
    WHERE c.classroom_id IN (
        -- Classes where teacher has subject assignments
        SELECT DISTINCT tsc.classroom_id
        FROM teacher_subject_classrooms tsc
        WHERE tsc.teacher_id = ?

        UNION

        -- Classes where teacher is the class teacher (wali kelas)
        SELECT DISTINCT c2.classroom_id
        FROM classrooms c2
        WHERE c2.teacher_id = ?
    )
    ORDER BY c.class_name
");
$classroomStmt->bind_param("ii", $teacherId, $teacherId);
$classroomStmt->execute();
$classrooms = $classroomStmt->get_result();

// Store classrooms in array for multiple use
$classroomList = [];
while ($classroom = $classrooms->fetch_assoc()) {
    $classroomList[] = $classroom;
}
$classroomStmt->close();

// Debug information (can be removed in production)
$debug_info = "Teacher ID: $teacherId | Total accessible classes: " . count($classroomList);
if (count($classroomList) > 0) {
    $debug_info .= " | Classes: " . implode(", ", array_column($classroomList, 'class_name'));
}

// Get selected classroom
$selectedClassroom = $_GET['classroom_id'] ?? '';
$selectedStudent = $_GET['student_id'] ?? '';

// Get students in selected classroom (only if teacher is assigned to that classroom)
$students = null;
if ($selectedClassroom) {
    // Verify teacher has access to this classroom (either through subject assignment or as class teacher)
    $accessStmt = $conn->prepare("
        SELECT COUNT(*) as count
        FROM (
            SELECT classroom_id FROM teacher_subject_classrooms WHERE teacher_id = ? AND classroom_id = ?
            UNION
            SELECT classroom_id FROM classrooms WHERE teacher_id = ? AND classroom_id = ?
        ) AS accessible_classrooms
    ");
    $accessStmt->bind_param("iiii", $teacherId, $selectedClassroom, $teacherId, $selectedClassroom);
    $accessStmt->execute();
    $accessResult = $accessStmt->get_result()->fetch_assoc();
    $accessStmt->close();

    if ($accessResult['count'] > 0) {
        $studentStmt = $conn->prepare("
            SELECT s.student_id, s.full_name
            FROM students s
            WHERE s.classroom_id = ?
            ORDER BY s.full_name
        ");
        $studentStmt->bind_param("i", $selectedClassroom);
        $studentStmt->execute();
        $students = $studentStmt->get_result();
        $studentStmt->close();
    } else {
        $selectedClassroom = ''; // Reset if no access
    }
}

// Get student assessments
$assessments = null;
$studentInfo = null;
if ($selectedStudent && $selectedClassroom) {
    // Verify teacher has access to this student's classroom (either through subject assignment or as class teacher)
    $studentAccessStmt = $conn->prepare("
        SELECT COUNT(*) as count
        FROM students s
        WHERE s.student_id = ? AND s.classroom_id IN (
            SELECT classroom_id FROM teacher_subject_classrooms WHERE teacher_id = ?
            UNION
            SELECT classroom_id FROM classrooms WHERE teacher_id = ?
        )
    ");
    $studentAccessStmt->bind_param("iii", $selectedStudent, $teacherId, $teacherId);
    $studentAccessStmt->execute();
    $studentAccessResult = $studentAccessStmt->get_result()->fetch_assoc();
    $studentAccessStmt->close();

    if ($studentAccessResult['count'] > 0) {
        // Get student info
        $studentInfoStmt = $conn->prepare("
            SELECT s.full_name, c.class_name
            FROM students s
            JOIN classrooms c ON s.classroom_id = c.classroom_id
            WHERE s.student_id = ?
        ");
        $studentInfoStmt->bind_param("i", $selectedStudent);
        $studentInfoStmt->execute();
        $studentInfo = $studentInfoStmt->get_result()->fetch_assoc();
        $studentInfoStmt->close();

        // Get assessments
        $assessmentStmt = $conn->prepare("
            SELECT a.assessment_type, s.subject_name, ar.marks, c.class_name,
                   CASE
                       WHEN ar.marks >= 90 THEN 'A+'
                       WHEN ar.marks >= 80 THEN 'A'
                       WHEN ar.marks >= 70 THEN 'A-'
                       WHEN ar.marks >= 65 THEN 'B+'
                       WHEN ar.marks >= 60 THEN 'B'
                       WHEN ar.marks >= 55 THEN 'C+'
                       WHEN ar.marks >= 50 THEN 'C'
                       WHEN ar.marks >= 45 THEN 'D'
                       WHEN ar.marks >= 40 THEN 'E'
                       ELSE 'G'
                   END as grade
            FROM assessment_result ar
            JOIN assessment a ON ar.assessment_id = a.assessment_id
            JOIN subjects s ON a.subject_id = s.subject_id
            JOIN classrooms c ON a.classroom_id = c.classroom_id
            WHERE ar.student_id = ?
            ORDER BY a.assessment_type, s.subject_name
        ");
        $assessmentStmt->bind_param("i", $selectedStudent);
        $assessmentStmt->execute();
        $assessments = $assessmentStmt->get_result();
        $assessmentStmt->close();
    } else {
        $selectedStudent = ''; // Reset if no access
    }
}
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lihat Markah Pelajar - SMKTMI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>

<style>
/* Content Styling */
.content {
    margin-left: 320px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.form-container {
    width: 100%;
    max-width: 1200px;
    background-color: #fff;
    padding: 30px 40px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0 auto;
}

.form-container h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 25px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    color: #2980b9;
    text-align: center;
}

.filter-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
    border-left: 4px solid #3498db;
}

.btn-primary {
    background-color: #3498db;
    border-color: #3498db;
    padding: 10px 30px;
    font-weight: 600;
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

.table th {
    background-color: rgb(153, 156, 158);
    color: white;
    text-align: center;
    font-weight: 600;
}

/* Grade styling - black font for all grades */
.grade {
    color: #000;
    font-weight: bold;
}

/* Responsive */
@media (max-width: 768px) {
    .content {
        margin-left: 0;
        padding: 20px;
    }
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
  <div class="form-container">
    <h2><i class="fas fa-chart-line"></i> Lihat Markah Pelajar</h2>

    <!-- Debug Information (remove in production) -->
    <?php if (isset($_GET['debug']) && $_GET['debug'] == '1'): ?>
    <div style="background-color: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; margin-bottom: 20px; border-radius: 5px; font-size: 12px;">
        <strong>Debug Info:</strong> <?= htmlspecialchars($debug_info) ?>
    </div>
    <?php endif; ?>

    <!-- Filter Section -->
    <div class="filter-section">
      <form method="GET" class="row g-3" id="filterForm">
        <div class="col-md-4">
          <label class="form-label"><strong>Pilih Kelas:</strong></label>
          <select name="classroom_id" class="form-select" id="classroomSelect" onchange="handleClassroomChange()">
            <option value="">-- Pilih Kelas --</option>
            <?php foreach ($classroomList as $classroom): ?>
              <option value="<?php echo $classroom['classroom_id']; ?>"
                      <?php echo ($selectedClassroom == $classroom['classroom_id']) ? 'selected' : ''; ?>>
                <?php echo htmlspecialchars($classroom['class_name']); ?>
              </option>
            <?php endforeach; ?>
          </select>
        </div>

        <?php if ($selectedClassroom && $students && $students->num_rows > 0): ?>
        <div class="col-md-4">
          <label class="form-label"><strong>Pilih Pelajar:</strong></label>
          <select name="student_id" class="form-select" id="studentSelect" onchange="handleStudentChange()">
            <option value="">-- Pilih Pelajar --</option>
            <?php
            // Reset the result pointer to the beginning
            $students->data_seek(0);
            while ($student = $students->fetch_assoc()): ?>
              <option value="<?php echo $student['student_id']; ?>"
                      <?php echo ($selectedStudent == $student['student_id']) ? 'selected' : ''; ?>>
                <?php echo htmlspecialchars($student['full_name']); ?>
              </option>
            <?php endwhile; ?>
          </select>
        </div>
        <?php elseif ($selectedClassroom && $students && $students->num_rows == 0): ?>
        <div class="col-md-4">
          <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle"></i> Tiada pelajar dalam kelas ini.
          </div>
        </div>
        <?php endif; ?>
      </form>
    </div>

    <?php if ($selectedStudent && $studentInfo): ?>
    <!-- Student Info -->
    <div class="alert alert-info">
      <h5><i class="fas fa-user-graduate"></i> Maklumat Pelajar</h5>
      <p><strong>Nama:</strong> <?php echo htmlspecialchars($studentInfo['full_name']); ?></p>
      <p><strong>Kelas:</strong> <?php echo htmlspecialchars($studentInfo['class_name']); ?></p>
    </div>

    <!-- Assessment Results -->
    <?php if ($assessments && $assessments->num_rows > 0): ?>
    <div class="table-responsive">
      <table class="table table-bordered">
        <thead>
          <tr>
            <th>Bil</th>
            <th>Jenis Penilaian</th>
            <th>Subjek</th>
            <th>Markah</th>
            <th>Gred</th>
          </tr>
        </thead>
        <tbody>
          <?php $bil = 1; while ($assessment = $assessments->fetch_assoc()): ?>
          <tr>
            <td class="text-center"><?php echo $bil++; ?></td>
            <td><?php echo htmlspecialchars($assessment['assessment_type']); ?></td>
            <td><?php echo htmlspecialchars($assessment['subject_name']); ?></td>
            <td class="text-center"><strong><?php echo (int)$assessment['marks']; ?></strong></td>
            <td class="text-center">
              <span class="grade">
                <?php echo $assessment['grade']; ?>
              </span>
            </td>
          </tr>
          <?php endwhile; ?>
        </tbody>
      </table>
    </div>
    <?php elseif ($selectedStudent && $studentInfo): ?>
    <div class="text-center" style="padding: 40px;">
      <i class="fas fa-clipboard-list" style="font-size: 48px; color: #bdc3c7; margin-bottom: 15px;"></i>
      <h5 style="color: #7f8c8d;">Tiada Rekod Penilaian</h5>
      <p style="color: #95a5a6;">Pelajar ini belum mempunyai rekod penilaian.</p>
    </div>
    <?php endif; ?>
    <?php endif; ?>

  </div>
</div>

<script>
function handleClassroomChange() {
    const classroomSelect = document.getElementById('classroomSelect');
    const selectedClassroom = classroomSelect.value;

    if (selectedClassroom) {
        // When classroom changes, redirect with only classroom_id (reset student selection)
        window.location.href = 'view_student_assessments.php?classroom_id=' + selectedClassroom;
    } else {
        // If no classroom selected, redirect to clean page
        window.location.href = 'view_student_assessments.php';
    }
}

function handleStudentChange() {
    const classroomSelect = document.getElementById('classroomSelect');
    const studentSelect = document.getElementById('studentSelect');
    const selectedClassroom = classroomSelect.value;
    const selectedStudent = studentSelect.value;

    if (selectedStudent && selectedClassroom) {
        // When student changes, redirect with both classroom_id and student_id
        window.location.href = 'view_student_assessments.php?classroom_id=' + selectedClassroom + '&student_id=' + selectedStudent;
    } else if (selectedClassroom) {
        // If student deselected but classroom still selected
        window.location.href = 'view_student_assessments.php?classroom_id=' + selectedClassroom;
    }
}
</script>

</body>
</html>
