<?php
session_start();
require 'db.php';

$error = "";
$verifiedMessage = "";
$warningMessage = "";
$isLockout = false;

if (isset($_GET['verified']) && $_GET['verified'] == 1) {
    $verifiedMessage = "Akaun anda telah disahkan. Sila log masuk.";
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email']);
    $password = $_POST['password'];

    $recaptcha_secret = '6Ld7NrUqAAAAAOad4ow7hDCy5F66R2MyvPKpY3s0';
    $recaptcha_response = $_POST['g-recaptcha-response'];
    $verify = file_get_contents("https://www.google.com/recaptcha/api/siteverify?secret={$recaptcha_secret}&response={$recaptcha_response}");
    $captcha_success = json_decode($verify);

    if (!$captcha_success->success) {
        $error = "Sila sahkan anda bukan robot.";
    } else {
        $stmt = $conn->prepare("SELECT * FROM users WHERE email = ?");
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();
        $user = $result->fetch_assoc();

        if ($user) {
            $userId = $user['user_id'];
            $failedAttempts = $user['failed_attempts'];
            $lockoutTime = $user['lockout_time'];

            $currentTime = time();

            if ($lockoutTime && strtotime($lockoutTime) > $currentTime) {
                $remaining = strtotime($lockoutTime) - $currentTime;
                $minutes = floor($remaining / 60);
                $seconds = $remaining % 60;
                $isLockout = true;
                if ($minutes > 0) {
                    $error = "🔒 Akaun dikunci selepas 3 cubaan yang gagal. Sila cuba lagi selepas $minutes minit dan $seconds saat.";
                } else {
                    $error = "🔒 Akaun dikunci selepas 3 cubaan yang gagal. Sila cuba lagi selepas $seconds saat.";
                }
            } else {
                if (password_verify($password, $user['password_hash'])) {
                    // Reset failed_attempts and lockout_time
                    $resetStmt = $conn->prepare("UPDATE users SET failed_attempts = 0, lockout_time = NULL WHERE user_id = ?");
                    $resetStmt->bind_param("i", $userId);
                    $resetStmt->execute();

                    $roleId = $user['role_id'];

                    if ($roleId == 2) {
                        $stmtCheck = $conn->prepare("SELECT email_verified FROM parents WHERE user_id = ?");
                        $stmtCheck->bind_param("i", $userId);
                        $stmtCheck->execute();
                        $resCheck = $stmtCheck->get_result();
                        $parentInfo = $resCheck->fetch_assoc();

                        if (!$parentInfo || $parentInfo['email_verified'] != 1) {
                            $error = "Sila sahkan akaun anda melalui emel sebelum log masuk.";
                        }
                    }

                    if (empty($error)) {
                        $_SESSION['user_id'] = $userId;
                        $_SESSION['role_id'] = $roleId;
                        $_SESSION['email'] = $user['email'];

                        switch ($roleId) {
                            case 1:
                                $stmt2 = $conn->prepare("SELECT admin_id, full_name FROM admins WHERE user_id = ?");
                                $_SESSION['role'] = 'Admin';
                                $redirect = "admin/dashboard.php";
                                break;
                            case 2:
                                $stmt2 = $conn->prepare("SELECT parent_id, full_name FROM parents WHERE user_id = ?");
                                $_SESSION['role'] = 'Parent';
                                $redirect = "parent/dashboard.php";
                                break;
                            case 3:
                                $stmt2 = $conn->prepare("SELECT teacher_id, full_name FROM teachers WHERE user_id = ?");
                                $_SESSION['role'] = 'Teacher';
                                $redirect = "teacher/dashboard.php";
                                break;
                            case 4:
                                $stmt2 = $conn->prepare("SELECT student_id, full_name FROM students WHERE user_id = ?");
                                $_SESSION['role'] = 'Student';
                                $redirect = "student/dashboard.php";
                                break;
                            default:
                                $error = "Unknown role.";
                                exit;
                        }

                        $stmt2->bind_param("i", $userId);
                        $stmt2->execute();
                        $result2 = $stmt2->get_result();
                        $info = $result2->fetch_assoc();
                        $_SESSION['name'] = $info ? $info['full_name'] : 'Unknown';

                        // Set role-specific IDs in session
                        if ($info) {
                            switch ($_SESSION['role']) {
                                case 'Admin':
                                    if (isset($info['admin_id'])) {
                                        $_SESSION['admin_id'] = $info['admin_id'];
                                    }
                                    break;
                                case 'Teacher':
                                    if (isset($info['teacher_id'])) {
                                        $_SESSION['teacher_id'] = $info['teacher_id'];
                                    }
                                    break;
                                case 'Student':
                                    if (isset($info['student_id'])) {
                                        $_SESSION['student_id'] = $info['student_id'];
                                    }
                                    break;
                                case 'Parent':
                                    if (isset($info['parent_id'])) {
                                        $_SESSION['parent_id'] = $info['parent_id'];
                                    }
                                    break;
                            }
                        }

                        if ($_SESSION['role'] === 'Admin') {
                            $currentTimestamp = date('Y-m-d H:i:s');
                            $updateStmt = $conn->prepare("UPDATE admins SET last_login = ? WHERE user_id = ?");
                            $updateStmt->bind_param("si", $currentTimestamp, $userId);
                            $updateStmt->execute();
                        }

                        header("Location: $redirect");
                        exit;
                    }
                } else {
                    $failedAttempts++;
                    if ($failedAttempts >= 3) {
                        $cooldownTime = date("Y-m-d H:i:s", $currentTime + 120); 
                        $updateFail = $conn->prepare("UPDATE users SET failed_attempts = ?, lockout_time = ? WHERE user_id = ?");
                        $updateFail->bind_param("isi", $failedAttempts, $cooldownTime, $userId);
                        $isLockout = true;
                        $error = "🔒 Akaun dikunci selepas 3 cubaan yang gagal. Sila cuba lagi selepas 2 minit.";
                    } else {
                        $updateFail = $conn->prepare("UPDATE users SET failed_attempts = ? WHERE user_id = ?");
                        $updateFail->bind_param("ii", $failedAttempts, $userId);
                        $remainingAttempts = 3 - $failedAttempts;
                        $error = "❌ Emel atau kata laluan tidak sah.";
                        $warningMessage = "⚠️ Amaran: Baki cubaan $remainingAttempts sebelum akaun dikunci selama 2 minit.";
                    }
                    $updateFail->execute();
                }
            }
        } else {
            $error = "Emel atau kata laluan tidak sah.";
        }
    }
}
?>

<!-- HTML Part -->
<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <title>Login - SMKTMI</title>
    <link rel="stylesheet" href="css/style.css">
    <!-- FontAwesome CDN for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            height: 100vh;
            overflow: hidden;
            position: relative;
            margin: 0;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Blurred Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('bangunan.jpg') center/cover no-repeat;
            filter: blur(8px);
            z-index: -2;
            transform: scale(1.1);
        }

        /* Static Background Overlay */
        .background-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: linear-gradient(135deg,
                rgba(37, 99, 235, 0.1) 0%,
                rgba(29, 78, 216, 0.15) 50%,
                rgba(30, 64, 175, 0.1) 100%);
        }

        /* Main Login Card - Split Layout */
        .login-card {
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            box-shadow:
                0 30px 60px rgba(0, 0, 0, 0.2),
                0 0 0 1px rgba(37, 99, 235, 0.2);
            overflow: hidden;
            max-width: 1000px;
            width: 90%;
            min-height: 600px;
            position: relative;
            z-index: 2;
        }

        /* Left Side - Logo Section */
        .logo-section {
            flex: 1;
            background: linear-gradient(135deg,
                rgba(37, 99, 235, 0.9) 0%,
                rgba(81, 119, 223, 0.95) 50%,
                rgba(30, 64, 175, 0.9) 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 60px 40px;
            position: relative;
            overflow: hidden;
        }

        .logo-container {
            position: relative;
            z-index: 2;
            text-align: center;
        }

        .logo-container img {
            width: 200px;
            height: 200px;
            border-radius: 25px;
            border: 5px solid rgba(255, 255, 255, 0.9);
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.3),
                0 0 0 3px rgba(37, 99, 235, 0.5);
            background: white;
            padding: 15px;
            margin-bottom: 30px;
        }

        .logo-text {
            color: white;
            text-align: center;
            position: relative;
            z-index: 2;
        }

        .logo-text h1 {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 15px;
            text-shadow: 2px 2px 8px rgba(0,0,0,0.3);
        }

        .logo-text p {
            font-size: 1.3rem;
            opacity: 0.95;
            text-shadow: 1px 1px 4px rgba(0,0,0,0.3);
            margin: 10px 0;
        }

        /* Right Side - Form Section */
        .form-section {
            flex: 1;
            padding: 60px 55px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(10px);
        }

        .form-header {
            text-align: center;
            margin-bottom: 10px;
        }

        .form-header h2 {
            font-size: 2.2rem;
            font-weight: 700;
            color: #1e40af;
            margin-bottom: 12px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
        }

        .form-header p {
            color: #1f2937;
            font-size: 1rem;
            margin: 0;
            font-weight: 600;
            text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
        }

        /* Form Styling */
        .form-group {
            margin-bottom: 24px;
        }

        .form-group label {
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
            color: #111827;
            font-size: 15px;
            text-shadow: 1px 1px 2px rgba(255,255,255,0.9);
        }

        .input-wrapper {
            position: relative;
        }

        .form-group input[type="email"],
        .form-group input[type="password"],
        .form-group input[type="text"] {
            width: 100%;
            padding: 16px 22px;
            border: 2px solid #9ca3af;
            border-radius: 12px;
            font-size: 15px;
            background: rgba(255, 255, 255, 0.98);
            outline: none;
            box-sizing: border-box;
            color: #111827;
            font-weight: 600;
        }

        .form-group input:focus {
            border-color: #2563eb;
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        /* Password Container Styling */
        .password-container {
            position: relative;
            width: 100%;
        }

        .password-container input[type="password"],
        .password-container input[type="text"] {
            width: 100%;
            padding: 16px 55px 16px 22px;
            border: 2px solid #9ca3af;
            border-radius: 12px;
            font-size: 15px;
            background: rgba(255, 255, 255, 0.98);
            outline: none;
            box-sizing: border-box;
            color: #111827;
            font-weight: 600;
        }

        .password-container input:focus {
            border-color: #2563eb;
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .password-toggle {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            cursor: pointer;
            color: #2563eb;
            font-size: 16px;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
            z-index: 10;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
        }

        .password-toggle:hover {
            color: #1d4ed8;
            background: rgba(37, 99, 235, 0.1);
            transform: translateY(-50%) scale(1.05);
        }

        .password-toggle:focus {
            outline: none;
        }

        /* Submit Button */
        button[type="submit"] {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 18px;
            box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4);
        }

        button[type="submit"]:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
            box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4);
        }

        .error {
            color: #dc3545;
            margin-bottom: 15px;
            font-weight: bold;
            padding: 12px;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            text-align: center;
        }

        .error.lockout {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }

        .warning {
            color: #856404;
            margin-bottom: 15px;
            font-weight: bold;
            padding: 12px;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            text-align: center;
        }

        .verified {
            color: green;
            margin-bottom: 10px;
            font-weight: bold;
        }

        /* Creative Registration Section */
        .registration-section {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 2px dashed #2563eb;
            border-radius: 16px;
            padding: 18px;
            margin: 20px 0;
            text-align: center;
            position: relative;
        }

        .registration-content {
            position: relative;
            z-index: 2;
        }

        .registration-title {
            color: #1e40af;
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            text-shadow: 1px 1px 2px rgba(255,255,255,0.9);
        }

        .registration-title i {
            font-size: 18px;
            color: #2563eb;
        }

        .registration-text {
            color: #1f2937;
            font-size: 14px;
            margin-bottom: 16px;
            line-height: 1.4;
            font-weight: 500;
            text-shadow: 1px 1px 2px rgba(255,255,255,0.9);
        }

        .registration-button {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 14px;
            box-shadow: 0 6px 20px rgba(37, 99, 235, 0.3);
        }

        .registration-button:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4);
            color: white;
            text-decoration: none;
        }

        .registration-button i {
            font-size: 15px;
        }

        .g-recaptcha {
            display: flex;
            justify-content: center;
            margin: 25px 0;
        }

        .security-info {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border: 2px solid #2563eb;
            border-radius: 12px;
            padding: 18px;
            margin: 22px 0;
            font-size: 14px;
            text-align: center;
        }

        .security-info p {
            margin: 6px 0;
            color: #1e40af;
            font-weight: 600;
            text-shadow: 1px 1px 2px rgba(255,255,255,0.9);
        }

        .security-info i {
            color: #2563eb;
            margin-right: 6px;
            font-size: 15px;
        }

        /* Static Decorative Elements */
        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 0;
        }

        .floating-element {
            position: absolute;
            background: rgba(37, 99, 235, 0.05);
            border-radius: 50%;
        }

        .floating-element:nth-child(1) {
            width: 80px;
            height: 80px;
            left: 10%;
            top: 20%;
        }

        .floating-element:nth-child(2) {
            width: 120px;
            height: 120px;
            right: 10%;
            top: 60%;
        }

        .floating-element:nth-child(3) {
            width: 60px;
            height: 60px;
            left: 50%;
            top: 80%;
        }

        .floating-element:nth-child(4) {
            width: 100px;
            height: 100px;
            left: 20%;
            top: 70%;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .login-card {
                max-width: 800px;
                min-height: 550px;
            }

            .logo-container img {
                width: 160px;
                height: 160px;
            }

            .logo-text h1 {
                font-size: 2.5rem;
            }

            .form-header h2 {
                font-size: 2.2rem;
            }
        }

        @media (max-width: 768px) {
            .login-card {
                flex-direction: column;
                max-width: 500px;
                min-height: auto;
                margin: 20px;
            }

            .logo-section {
                padding: 40px 30px;
                min-height: 300px;
            }

            .form-section {
                padding: 40px 30px;
            }

            .logo-container img {
                width: 120px;
                height: 120px;
            }

            .logo-text h1 {
                font-size: 2rem;
            }

            .logo-text p {
                font-size: 1.1rem;
            }

            .form-header h2 {
                font-size: 1.8rem;
            }
        }

        @media (max-width: 480px) {
            .login-card {
                margin: 10px;
                border-radius: 20px;
            }

            .logo-section {
                padding: 30px 20px;
                min-height: 250px;
            }

            .form-section {
                padding: 30px 20px;
            }

            .logo-container img {
                width: 100px;
                height: 100px;
            }

            .logo-text h1 {
                font-size: 1.6rem;
            }

            .form-header h2 {
                font-size: 1.5rem;
            }

            .form-group input {
                padding: 15px 20px;
                font-size: 14px;
            }

            button[type="submit"] {
                padding: 15px;
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
<!-- Animated Background Overlay -->
<div class="background-overlay"></div>

<!-- Floating Elements -->
<div class="floating-elements">
    <div class="floating-element"></div>
    <div class="floating-element"></div>
    <div class="floating-element"></div>
    <div class="floating-element"></div>
</div>

<div class="login-card">
    <!-- Left Side - Logo Section -->
    <div class="logo-section">
        <div class="logo-container">
            <img src="logo smktmi.jpg" alt="Logo SMKTMI">
        </div>
        <div class="logo-text">
            <h1>SMKTMI</h1>
            <p>Sistem Maklumat Sekolah</p>
            <p>Portal Pendidikan Digital</p>
            <p>Selamat Datang</p>
        </div>
    </div>

    <!-- Right Side - Form Section -->
    <div class="form-section">
        <div class="form-header">
            <h2>Log Masuk</h2>
            <p>Sila masukkan maklumat akaun anda</p>
        </div>

    <?php if (!empty($verifiedMessage)): ?>
        <div class="verified"><?php echo htmlspecialchars($verifiedMessage); ?></div>
    <?php endif; ?>

    <?php if (!empty($error)): ?>
        <div class="error <?php echo $isLockout ? 'lockout' : ''; ?>"><?php echo htmlspecialchars($error); ?></div>
    <?php endif; ?>

    <?php if (!empty($warningMessage)): ?>
        <div class="warning"><?php echo htmlspecialchars($warningMessage); ?></div>
    <?php endif; ?>

        <form method="post" action="" autocomplete="on" id="loginForm">
            <div class="form-group">
                <label for="email">Email:</label>
                <div class="input-wrapper">
                    <input type="email" id="email" name="email" autocomplete="email" required>
                </div>
            </div>

            <div class="form-group">
                <label for="password">Kata Laluan:</label>
                <div class="password-container">
                    <input type="password" id="password" name="password" autocomplete="current-password" required>
                    <button type="button" class="password-toggle" onclick="togglePassword()">
                        <i class="fas fa-eye" id="toggleIcon"></i>
                    </button>
                </div>
            </div>

            <div class="form-group">
                <div class="g-recaptcha" data-sitekey="6Ld7NrUqAAAAALxE-JVIlkfCALZK6eWfrLTP9AaE"></div>
            </div>

            <button type="submit" id="submitBtn">
                <span id="btnText">Log Masuk</span>
            </button>
        </form>

        <!-- Security Information -->
        <div class="security-info">
            <p><i class="fas fa-shield-alt"></i> <strong>Keselamatan Akaun:</strong></p>
            <p>• Maksimum 3 cubaan log masuk yang gagal</p>
            <p>• Akaun akan dikunci selama 2 minit selepas 3 cubaan gagal</p>
        </div>

        <!-- Creative Registration Section -->
        <div class="registration-section">
            <div class="registration-content">
                <div class="registration-title">
                    <i class="fas fa-user-plus"></i>
                    Belum Mempunyai Akaun?
                </div>
                <div class="registration-text">
                    Daftar sebagai Ibu Bapa / Penjaga untuk memantau perkembangan akademik anak anda secara dalam talian
                </div>
                <a href="register.php" class="registration-button">
                    <i class="fas fa-arrow-right"></i>
                    Daftar Sekarang
                </a>
            </div>
        </div>
    </div>
</div>

<script src="https://www.google.com/recaptcha/api.js" async defer></script>

<script>
// Enhanced interactive features for split layout
document.addEventListener('DOMContentLoaded', function() {
    // Form submission with loading animation
    const form = document.getElementById('loginForm');
    const submitBtn = document.getElementById('submitBtn');
    const btnText = document.getElementById('btnText');

    if (form && submitBtn && btnText) {
        form.addEventListener('submit', function(e) {
            submitBtn.disabled = true;
            btnText.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sedang Log Masuk...';
            submitBtn.style.background = 'linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%)';
        });
    }


});

function togglePassword() {
    const passwordField = document.getElementById('password');
    const toggleIcon = document.getElementById('toggleIcon');

    if (passwordField && toggleIcon) {
        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    }
}

// Lockout countdown timer
<?php if ($isLockout && !empty($error)): ?>
document.addEventListener('DOMContentLoaded', function() {
    const errorDiv = document.querySelector('.error.lockout');
    if (errorDiv) {
        // Extract remaining time from PHP
        const errorText = errorDiv.textContent;
        const timeMatch = errorText.match(/(\d+)\s*minit.*?(\d+)\s*saat|(\d+)\s*saat/);

        let totalSeconds = 0;
        if (timeMatch) {
            if (timeMatch[1] && timeMatch[2]) {
                // Minutes and seconds
                totalSeconds = parseInt(timeMatch[1]) * 60 + parseInt(timeMatch[2]);
            } else if (timeMatch[3]) {
                // Only seconds
                totalSeconds = parseInt(timeMatch[3]);
            }
        }

        if (totalSeconds > 0) {
            const countdown = setInterval(function() {
                totalSeconds--;

                const minutes = Math.floor(totalSeconds / 60);
                const seconds = totalSeconds % 60;

                if (minutes > 0) {
                    errorDiv.textContent = `🔒 Akaun dikunci selepas 3 cubaan yang gagal. Sila cuba lagi selepas ${minutes} minit dan ${seconds} saat.`;
                } else {
                    errorDiv.textContent = `🔒 Akaun dikunci selepas 3 cubaan yang gagal. Sila cuba lagi selepas ${seconds} saat.`;
                }

                if (totalSeconds <= 0) {
                    clearInterval(countdown);
                    errorDiv.textContent = '✅ Tempoh lockout tamat. Sila refresh halaman untuk cuba log masuk semula.';
                    errorDiv.style.backgroundColor = '#d4edda';
                    errorDiv.style.borderColor = '#c3e6cb';
                    errorDiv.style.color = '#155724';

                    // Auto refresh after 3 seconds
                    setTimeout(function() {
                        window.location.reload();
                    }, 3000);
                }
            }, 1000);
        }
    }
});
<?php endif; ?>
</script>

</body>
</html>
