<?php
session_start();

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Teacher') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';
require '../includes/universal_logger.php';

$userId = $_SESSION['user_id'];

// Fetch teacher details
$stmt = $conn->prepare("SELECT teacher_id, full_name, staff_id, phone_number FROM teachers WHERE user_id = ?");
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();
$teacher = $result->fetch_assoc();

$teacherId = $teacher['teacher_id'] ?? null;

// Check if teacher is a classroom teacher
$classStmt = $conn->prepare("SELECT class_name FROM classrooms WHERE teacher_id = ?");
$classStmt->bind_param("i", $teacherId);
$classStmt->execute();
$classResult = $classStmt->get_result();
$classInfo = $classResult->fetch_assoc();
$className = $classInfo['class_name'] ?? null;

// Fetch teaching assignments
$assignStmt = $conn->prepare("
    SELECT s.subject_name, c.class_name
    FROM teacher_subject_classrooms tsc
    JOIN subjects s ON tsc.subject_id = s.subject_id
    JOIN classrooms c ON tsc.classroom_id = c.classroom_id
    WHERE tsc.teacher_id = ?
");
$assignStmt->bind_param("i", $teacherId);
$assignStmt->execute();
$assignments = $assignStmt->get_result();

// Get assessment statistics for this teacher
$assessment_stats = [];
if ($teacherId) {
    // Check if assessment tables exist
    $table_check = $conn->query("SHOW TABLES LIKE 'assessment'");
    if ($table_check && $table_check->num_rows > 0) {
        try {
            $stats_stmt = $conn->prepare("
                SELECT
                    COUNT(DISTINCT a.assessment_id) as total_assessments,
                    COUNT(DISTINCT CASE WHEN ar.assessment_id IS NULL THEN a.assessment_id END) as pending_assessments,
                    COUNT(DISTINCT CASE WHEN ar.assessment_id IS NOT NULL THEN a.assessment_id END) as completed_assessments
                FROM assessment a
                JOIN teacher_subject_classrooms tsc ON (tsc.subject_id = a.subject_id AND tsc.classroom_id = a.classroom_id)
                LEFT JOIN assessment_result ar ON a.assessment_id = ar.assessment_id
                WHERE tsc.teacher_id = ?
            ");
            $stats_stmt->bind_param("i", $teacherId);
            $stats_stmt->execute();
            $result = $stats_stmt->get_result();
            if ($row = $result->fetch_assoc()) {
                $assessment_stats = $row;
            }
            $stats_stmt->close();
        } catch (mysqli_sql_exception $e) {
            // Assessment tables don't exist or have issues
            $assessment_stats = ['total_assessments' => 0, 'pending_assessments' => 0, 'completed_assessments' => 0];
        }
    } else {
        // Assessment tables don't exist
        $assessment_stats = ['total_assessments' => 0, 'pending_assessments' => 0, 'completed_assessments' => 0];
    }
}

// Handle update
$updateMsg = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $updatedName = trim($_POST['full_name']);
    $updatedPhone = trim($_POST['phone_number']);

    $updateStmt = $conn->prepare("UPDATE teachers SET full_name = ?, phone_number = ? WHERE teacher_id = ?");
    $updateStmt->bind_param("ssi", $updatedName, $updatedPhone, $teacherId);
    if ($updateStmt->execute()) {
        // Log the teacher profile update with details
        $details = [
            'Nama Lama' => $teacher['full_name'],
            'Nama Baru' => $updatedName,
            'Telefon Lama' => $teacher['phone_number'],
            'Telefon Baru' => $updatedPhone
        ];
        logUserActivity($conn, 'UPDATE', 'PROFILE', $teacherId, $updatedName,
            "Maklumat guru dikemaskini", $details);

        $updateMsg = "Maklumat berjaya dikemaskini.";
        $teacher['full_name'] = $updatedName;
        $teacher['phone_number'] = $updatedPhone;
        $_SESSION['name'] = $updatedName;
    } else {
        $updateMsg = "Gagal kemaskini maklumat.";
    }
}
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Guru - SMKTMI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>

<style>
/* Content Styling */
.content {
    margin-left: 280px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.form-container {
    width: 100%;
    max-width: 1200px;
    background-color: #fff;
    padding: 30px 40px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0 auto;
}

.form-container h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 25px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    color: #2980b9;
    text-align: center;
}
.alert {
    padding: 15px;
    background-color: #d9edf7;
    border: 1px solid #bce8f1;
    border-radius: 5px;
    color: #31708f;
    margin-bottom: 25px;
    text-align: center;
}

.alert-success {
    background-color: #dff0d8;
    border-color: #d6e9c6;
    color: #3c763d;
}

.stats-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-left: 4px solid #3498db;
    transition: transform 0.2s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.info-section {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
    border-left: 4px solid #3498db;
}

.btn-primary {
    background-color: #3498db;
    border-color: #3498db;
    padding: 10px 30px;
    font-weight: 600;
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

.btn-secondary {
    background-color: #95a5a6;
    border-color: #95a5a6;
    padding: 10px 20px;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
    border-color: #7f8c8d;
}

/* Responsive */
@media (max-width: 768px) {
    .content {
        margin-left: 0;
        padding: 20px;
    }
}

</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
  <div class="form-container">
    <h2><i class="fas fa-tachometer-alt"></i> Dashboard Guru</h2>

    <?php if (!empty($updateMsg)): ?>
      <div class="alert alert-success">
        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($updateMsg); ?>
      </div>
    <?php endif; ?>

    <!-- Welcome Section -->
    <div class="info-section">
      <h4 style="margin-bottom: 15px; color: #2c3e50;">
        <i class="fas fa-user-tie"></i> Selamat Datang, <?php echo htmlspecialchars($teacher['full_name']); ?>!
      </h4>
      <?php if ($className): ?>
        <p style="margin-bottom: 15px; color: #2980b9; font-weight: 600;">
          <i class="fas fa-chalkboard-teacher"></i> Anda adalah Guru Kelas <?php echo htmlspecialchars($className); ?>
        </p>
      <?php endif; ?>

      <!-- Security Notice -->
      <div class="alert alert-info" style="margin-bottom: 20px;">
        <i class="fas fa-shield-alt"></i>
        <strong>Keselamatan Akaun:</strong>
        Untuk keselamatan akaun anda, sila
        <a href="change_password.php" class="alert-link">
          <i class="fas fa-key"></i> tukar kata laluan
        </a>
        jika ini adalah log masuk pertama anda atau jika kata laluan anda ditetapkan oleh pentadbir.
      </div>
    </div>

    <!-- Personal Information and Teaching Assignments -->
    <div class="row">
      <div class="col-md-6">
        <div class="stats-card">
          <h5 style="margin-bottom: 20px; color: #2c3e50;">
            <i class="fas fa-user-edit"></i> Maklumat Peribadi
          </h5>
          <form method="post">
            <div class="mb-3">
              <label class="form-label"><strong>Nama Penuh:</strong></label>
              <input type="text" class="form-control" name="full_name"
                     value="<?php echo htmlspecialchars($teacher['full_name']); ?>" required>
            </div>
            <div class="mb-3">
              <label class="form-label"><strong>ID Staf:</strong></label>
              <input type="text" class="form-control"
                     value="<?php echo htmlspecialchars($teacher['staff_id']); ?>" readonly>
            </div>
            <div class="mb-3">
              <label class="form-label"><strong>Nombor Telefon:</strong></label>
              <input type="text" class="form-control" name="phone_number"
                     value="<?php echo htmlspecialchars($teacher['phone_number']); ?>">
            </div>
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-save"></i> Kemaskini Maklumat
            </button>
          </form>
        </div>
      </div>

      <div class="col-md-6">
        <div class="stats-card">
          <h5 style="margin-bottom: 20px; color: #2c3e50;">
            <i class="fas fa-chalkboard-teacher"></i> Tugasan Mengajar
          </h5>
          <table class="table table-bordered">
            <thead class="table-light">
              <tr>
                <th>Subjek</th>
                <th>Kelas</th>
              </tr>
            </thead>
            <tbody>
              <?php if ($assignments->num_rows > 0): ?>
                <?php while ($row = $assignments->fetch_assoc()): ?>
                  <tr>
                    <td><?php echo htmlspecialchars($row['subject_name']); ?></td>
                    <td><?php echo htmlspecialchars($row['class_name']); ?></td>
                  </tr>
                <?php endwhile; ?>
              <?php else: ?>
                <tr><td colspan="2" class="text-center text-muted">Tiada Tugasan Mengajar</td></tr>
              <?php endif; ?>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Assessment Statistics Section -->
    <?php if (!empty($assessment_stats) && $assessment_stats['total_assessments'] > 0): ?>
      <div class="stats-card" style="margin-top: 30px;">
        <h5 style="margin-bottom: 20px; color: #2c3e50;">
          <i class="fas fa-chart-bar"></i> Statistik Penilaian
        </h5>
        <div class="row text-center">
          <div class="col-md-4">
            <div style="background: #3498db; color: white; padding: 20px; border-radius: 8px; margin-bottom: 15px;">
              <div style="font-size: 32px; font-weight: bold;"><?php echo $assessment_stats['total_assessments']; ?></div>
              <div style="font-size: 14px; opacity: 0.9;">Jumlah Penilaian</div>
            </div>
          </div>
          <div class="col-md-4">
            <div style="background: #f39c12; color: white; padding: 20px; border-radius: 8px; margin-bottom: 15px;">
              <div style="font-size: 32px; font-weight: bold;"><?php echo $assessment_stats['pending_assessments']; ?></div>
              <div style="font-size: 14px; opacity: 0.9;">Belum Selesai</div>
            </div>
          </div>
          <div class="col-md-4">
            <div style="background: #27ae60; color: white; padding: 20px; border-radius: 8px; margin-bottom: 15px;">
              <div style="font-size: 32px; font-weight: bold;"><?php echo $assessment_stats['completed_assessments']; ?></div>
              <div style="font-size: 14px; opacity: 0.9;">Selesai</div>
            </div>
          </div>
        </div>
        <div class="text-center" style="margin-top: 20px;">
          <a href="mark_assessment.php" class="btn btn-primary">
            <i class="fas fa-pencil-alt"></i> Urus Markah Penilaian
          </a>
        </div>
      </div>
    <?php elseif ($teacherId): ?>
      <div class="stats-card" style="margin-top: 30px; text-align: center; padding: 40px;">
        <i class="fas fa-clipboard-list" style="font-size: 64px; color: #bdc3c7; margin-bottom: 20px;"></i>
        <h4 style="color: #7f8c8d; margin-bottom: 15px;">Tiada Penilaian Dijumpai</h4>
        <p style="color: #95a5a6; margin-bottom: 20px;">
          Tiada penilaian yang ditetapkan untuk subjek dan kelas yang anda ajar.
          <br>Sila hubungi pentadbir untuk maklumat lanjut.
        </p>
        <a href="mark_assessment.php" class="btn btn-secondary">
          <i class="fas fa-search"></i> Lihat Penilaian
        </a>
      </div>
    <?php endif; ?>
  </div>
</div>

</body>
</html>
