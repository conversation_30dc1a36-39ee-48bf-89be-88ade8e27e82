<?php
session_start();
require '../db.php';

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header("Location: ../login.php");
    exit;
}

$message = "";

// Generate CSRF token
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Jika borang dihantar
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $message = "❌ Invalid form submission.";
    } else {
        $teacher_id = isset($_POST['teacher_id']) ? (int)$_POST['teacher_id'] : 0;
        $subject_id = isset($_POST['subject_id']) ? (int)$_POST['subject_id'] : 0;
        $classroom_id = isset($_POST['classroom_id']) ? (int)$_POST['classroom_id'] : 0;

        if (!$teacher_id || !$subject_id || !$classroom_id) {
            $message = "❌ Sila lengkapkan semua medan.";
        } else {
            $check = $conn->prepare("SELECT * FROM teacher_subject_classrooms WHERE subject_id = ? AND classroom_id = ?");
            $check->bind_param("ii", $subject_id, $classroom_id);
            $check->execute();
            $result = $check->get_result();

            if ($result->num_rows > 0) {
                $message = "⚠️ Subjek ini telahpun diajar dalam kelas tersebut oleh guru lain.";
            } else {
                $insert = $conn->prepare("INSERT INTO teacher_subject_classrooms (teacher_id, subject_id, classroom_id) VALUES (?, ?, ?)");
                $insert->bind_param("iii", $teacher_id, $subject_id, $classroom_id);

                if ($insert->execute()) {
                    $message = "✅ Guru berjaya ditetapkan.";
                } else {
                    $message = "❌ Ralat semasa menetapkan guru. Sila cuba lagi.";
                }
            }
        }
    }
}

$teachers = $conn->query("SELECT teacher_id, full_name FROM teachers ORDER BY full_name");
$subjects = $conn->query("SELECT subject_id, subject_name FROM subjects ORDER BY subject_name");
$classrooms = $conn->query("SELECT classroom_id, class_name FROM classrooms ORDER BY class_name");

// Ambil semua tetapan sedia ada, ordered by subject_name and class_name
$assignments = $conn->query("
    SELECT tsc.*, t.full_name, s.subject_name, c.class_name
    FROM teacher_subject_classrooms tsc
    JOIN teachers t ON tsc.teacher_id = t.teacher_id
    JOIN subjects s ON tsc.subject_id = s.subject_id
    JOIN classrooms c ON tsc.classroom_id = c.classroom_id
    ORDER BY s.subject_name, c.class_name
");

// Group assignments by subject_name
$grouped = [];
if ($assignments) {
    while ($row = $assignments->fetch_assoc()) {
        $subject = $row['subject_name'];
        if (!isset($grouped[$subject])) {
            $grouped[$subject] = [];
        }
        $grouped[$subject][] = [
            'teacher' => $row['full_name'],
            'class' => $row['class_name'],
        ];
    }
}

$selected_teacher = $_POST['teacher_id'] ?? '';
$selected_subject = $_POST['subject_id'] ?? '';
$selected_classroom = $_POST['classroom_id'] ?? '';
?>

<?php include 'includes/header.php'; ?>

<!-- FontAwesome CDN for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<style>
/* Sidebar Styling */
.sidebar {
    position: fixed;
    top: 0; left: 0;
    width: 300px;
    height: 100vh;
    background-color: #2c3e50;
    padding: 30px 20px;
    color: #ecf0f1;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow-y: auto;
    box-shadow: 5px 0 15px rgba(0,0,0,0.4);
    border-right: 1px solid #34495e;
}

.sidebar h4 {
    font-size: 18px;
    font-weight: 700;
    text-transform: uppercase;
    margin: 30px 0 12px;
    border-bottom: 2px solid #3498db;
    padding-bottom: 6px;
    letter-spacing: 1.5px;
}

.sidebar a {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    color: #bdc3c7;
    text-decoration: none;
    font-size: 16px;
    margin-bottom: 8px;
    border-radius: 6px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.sidebar a i {
    margin-right: 12px;
    width: 18px;
    text-align: center;
}

.sidebar a:hover {
    background-color: #2980b9;
    color: #ecf0f1;
}

/* Content Styling */
.content {
    margin-left: 280px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.form-container {
    width: 100%;
    max-width: 1400px;
    background-color: #fff;
    padding: 30px 40px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0 auto;
}

.form-container h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 25px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    color: #2980b9;
    text-align: center;
}

.alert {
    padding: 15px;
    background-color: #d9edf7;
    border: 1px solid #bce8f1;
    border-radius: 5px;
    color: #31708f;
    margin-bottom: 25px;
    text-align: center;
}

.form-section {
    display: flex;
    gap: 30px;
    align-items: flex-start;
}

.form-left {
    flex: 0 0 400px;
    max-width: 400px;
}

.form-right {
    flex: 1;
    min-width: 600px;
}

.form-left h4 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 8px;
}

.form-right h4 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 8px;
}

/* Form Styling */
form {
    margin-bottom: 20px;
}

label {
    font-weight: 700;
    margin-bottom: 6px;
    display: block;
    color: #34495e;
}

select {
    width: 100%;
    padding: 10px 14px;
    font-size: 16px;
    border: 1.5px solid #ccc;
    border-radius: 6px;
    transition: border-color 0.3s ease;
    margin-bottom: 15px;
}

select:focus {
    border-color: #2980b9;
    outline: none;
}

button[type="submit"] {
    width: 100%;
    padding: 12px 0;
    background-color: #2980b9;
    border: none;
    color: #ecf0f1;
    font-size: 16px;
    font-weight: 700;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-top: 10px;
}

button[type="submit"]:hover {
    background-color: #1a5d8f;
}

/* Table Styling */
.assignment-list {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 25px;
    max-height: 600px;
    overflow-y: auto;
}

.table {
    margin-bottom: 0;
    font-size: 15px;
}

.table th {
    background-color: #2c3e50;
    color: white;
    font-weight: 600;
    border: none;
    padding: 12px 15px;
    font-size: 16px;
}

.table td {
    vertical-align: middle;
    padding: 10px 15px;
    border-bottom: 1px solid #dee2e6;
}

/* All subject headers use the same color */
.table-primary td,
.subject-bahasa-inggeris td,
.subject-bahasa-melayu td,
.subject-sains td,
.subject-sejarah td,
.subject-header td {
    background-color: rgb(108, 165, 223) !important;
    color: white !important;
    font-weight: bold;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
        box-shadow: none;
    }
    .content {
        margin-left: 0;
        padding: 20px;
    }
    .form-section {
        flex-direction: column;
    }
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
    <div class="form-container">
        <h2>Tetapan Guru-Subjek-Kelas</h2>

        <?php if ($message): ?>
            <div class="alert"><?= htmlspecialchars($message) ?></div>
        <?php endif; ?>

        <div class="form-section">
            <!-- Form section -->
            <div class="form-left">
                <h4>Tetapkan Guru kepada Subjek dan Kelas</h4>
                <form method="POST" action="">
                    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token']) ?>">

                    <label for="teacher_id">Guru</label>
                    <select name="teacher_id" id="teacher_id" required>
                        <option value="">-- Pilih Guru --</option>
                        <?php
                        if ($teachers) {
                            $teachers->data_seek(0);
                            while ($t = $teachers->fetch_assoc()):
                        ?>
                            <option value="<?= (int)$t['teacher_id'] ?>" <?= ($selected_teacher == $t['teacher_id']) ? 'selected' : '' ?>>
                                <?= htmlspecialchars($t['full_name']) ?>
                            </option>
                        <?php endwhile; } ?>
                    </select>

                    <label for="subject_id">Subjek</label>
                    <select name="subject_id" id="subject_id" required>
                        <option value="">-- Pilih Subjek --</option>
                        <?php
                        if ($subjects) {
                            $subjects->data_seek(0);
                            while ($s = $subjects->fetch_assoc()):
                        ?>
                            <option value="<?= (int)$s['subject_id'] ?>" <?= ($selected_subject == $s['subject_id']) ? 'selected' : '' ?>>
                                <?= htmlspecialchars($s['subject_name']) ?>
                            </option>
                        <?php endwhile; } ?>
                    </select>

                    <label for="classroom_id">Kelas</label>
                    <select name="classroom_id" id="classroom_id" required>
                        <option value="">-- Pilih Kelas --</option>
                        <?php
                        if ($classrooms) {
                            $classrooms->data_seek(0);
                            while ($c = $classrooms->fetch_assoc()):
                        ?>
                            <option value="<?= (int)$c['classroom_id'] ?>" <?= ($selected_classroom == $c['classroom_id']) ? 'selected' : '' ?>>
                                <?= htmlspecialchars($c['class_name']) ?>
                            </option>
                        <?php endwhile; } ?>
                    </select>

                    <button type="submit">Tetapkan Guru</button>
                </form>
            </div>

            <!-- Assignment list -->
            <div class="form-right">
                <div class="assignment-list">
                    <h4>Senarai Guru-Subjek-Kelas (Mengikut Subjek)</h4>
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Subjek</th>
                                <th>Kelas</th>
                                <th>Guru</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($grouped)): ?>
                                <?php foreach ($grouped as $subject => $assignments): ?>
                                    <tr class="subject-header">
                                        <td colspan="3"><?= htmlspecialchars($subject) ?></td>
                                    </tr>
                                    <?php foreach ($assignments as $assign): ?>
                                        <tr>
                                            <td></td> <!-- Empty to keep subject row separate -->
                                            <td><?= htmlspecialchars($assign['class']) ?></td>
                                            <td><?= htmlspecialchars($assign['teacher']) ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr><td colspan="3" style="text-align: center; color: #7f8c8d; font-style: italic;">Tiada tetapan lagi.</td></tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
