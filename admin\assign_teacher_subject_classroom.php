<?php
session_start();

// Check if database connection file exists
if (!file_exists('../db.php')) {
    die("Error: Database connection file not found.");
}

require '../db.php';

// Check if database connection is established
if (!isset($conn) || $conn->connect_error) {
    die("Error: Database connection failed: " . ($conn->connect_error ?? 'Unknown error'));
}

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header("Location: ../login.php");
    exit;
}

$message = "";

// Generate CSRF token
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Handle POST requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $message = "❌ Invalid form submission.";
    } else {
        $action = $_POST['action'] ?? 'assign';

        if ($action === 'delete') {
            // Handle delete operation
            $tsc_id = isset($_POST['tsc_id']) ? (int)$_POST['tsc_id'] : 0;
            if ($tsc_id > 0) {
                $delete = $conn->prepare("DELETE FROM teacher_subject_classrooms WHERE tsc_id = ?");
                if ($delete) {
                    $delete->bind_param("i", $tsc_id);
                    if ($delete->execute()) {
                        $message = "✅ Tetapan guru berjaya dipadam.";
                    } else {
                        $message = "❌ Ralat semasa memadam tetapan guru: " . $delete->error;
                    }
                    $delete->close();
                } else {
                    $message = "❌ Ralat menyediakan query padam: " . $conn->error;
                }
            } else {
                $message = "❌ ID tetapan tidak sah.";
            }
        } else {
            // Handle new assignment (default action)
            $teacher_id = isset($_POST['teacher_id']) ? (int)$_POST['teacher_id'] : 0;
            $subject_id = isset($_POST['subject_id']) ? (int)$_POST['subject_id'] : 0;
            $classroom_id = isset($_POST['classroom_id']) ? (int)$_POST['classroom_id'] : 0;

            // Additional validation
            if ($teacher_id < 1 || $subject_id < 1 || $classroom_id < 1) {
                $message = "❌ Nilai ID tidak sah.";
            } else {

                if (!$teacher_id || !$subject_id || !$classroom_id) {
                    $message = "❌ Sila lengkapkan semua medan.";
                } else {
                $check = $conn->prepare("SELECT * FROM teacher_subject_classrooms WHERE subject_id = ? AND classroom_id = ?");
                if ($check) {
                    $check->bind_param("ii", $subject_id, $classroom_id);
                    $check->execute();
                    $result = $check->get_result();

                    if ($result->num_rows > 0) {
                        $message = "⚠️ Subjek ini telahpun diajar dalam kelas tersebut oleh guru lain.";
                    } else {
                        $insert = $conn->prepare("INSERT INTO teacher_subject_classrooms (teacher_id, subject_id, classroom_id) VALUES (?, ?, ?)");
                        if ($insert) {
                            $insert->bind_param("iii", $teacher_id, $subject_id, $classroom_id);

                            if ($insert->execute()) {
                                $message = "✅ Guru berjaya ditetapkan.";
                            } else {
                                $message = "❌ Ralat semasa menetapkan guru: " . $insert->error;
                            }
                            $insert->close();
                        } else {
                            $message = "❌ Ralat menyediakan query insert: " . $conn->error;
                        }
                    }
                    $check->close();
                } else {
                    $message = "❌ Ralat menyediakan query check: " . $conn->error;
                }
                }
            }
        }
    }
}

// Fetch data for dropdowns with error handling
$teachers_result = $conn->query("SELECT teacher_id, full_name FROM teachers ORDER BY full_name");
if (!$teachers_result) {
    die("Error fetching teachers: " . $conn->error);
}
$teachers = [];
while ($teacher = $teachers_result->fetch_assoc()) {
    $teachers[] = $teacher;
}

$subjects_result = $conn->query("SELECT subject_id, subject_name FROM subjects ORDER BY subject_name");
if (!$subjects_result) {
    die("Error fetching subjects: " . $conn->error);
}
$subjects = [];
while ($subject = $subjects_result->fetch_assoc()) {
    $subjects[] = $subject;
}

$classrooms_result = $conn->query("SELECT classroom_id, class_name FROM classrooms ORDER BY class_name");
if (!$classrooms_result) {
    die("Error fetching classrooms: " . $conn->error);
}
$classrooms = [];
while ($classroom = $classrooms_result->fetch_assoc()) {
    $classrooms[] = $classroom;
}

// Ambil semua tetapan sedia ada, ordered by subject_name and class_name
$assignments = $conn->query("
    SELECT tsc.tsc_id, tsc.teacher_id, tsc.subject_id, tsc.classroom_id,
           t.full_name, s.subject_name, c.class_name
    FROM teacher_subject_classrooms tsc
    JOIN teachers t ON tsc.teacher_id = t.teacher_id
    JOIN subjects s ON tsc.subject_id = s.subject_id
    JOIN classrooms c ON tsc.classroom_id = c.classroom_id
    ORDER BY s.subject_name, c.class_name
");

if (!$assignments) {
    die("Error fetching assignments: " . $conn->error);
}

// Group assignments by subject_name
$grouped = [];
if ($assignments && $assignments->num_rows > 0) {
    while ($row = $assignments->fetch_assoc()) {
        $subject = $row['subject_name'];
        if (!isset($grouped[$subject])) {
            $grouped[$subject] = [];
        }
        $grouped[$subject][] = [
            'tsc_id' => $row['tsc_id'],
            'teacher_id' => $row['teacher_id'],
            'subject_id' => $row['subject_id'],
            'classroom_id' => $row['classroom_id'],
            'teacher' => $row['full_name'],
            'class' => $row['class_name'],
        ];
    }
}

$selected_teacher = $_POST['teacher_id'] ?? '';
$selected_subject = $_POST['subject_id'] ?? '';
$selected_classroom = $_POST['classroom_id'] ?? '';
?>

<?php include 'includes/header.php'; ?>

<!-- FontAwesome CDN for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<style>
/* Sidebar Styling */
.sidebar {
    position: fixed;
    top: 0; left: 0;
    width: 300px;
    height: 100vh;
    background-color: #2c3e50;
    padding: 30px 20px;
    color: #ecf0f1;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow-y: auto;
    box-shadow: 5px 0 15px rgba(0,0,0,0.4);
    border-right: 1px solid #34495e;
}

.sidebar h4 {
    font-size: 18px;
    font-weight: 700;
    text-transform: uppercase;
    margin: 30px 0 12px;
    border-bottom: 2px solid #3498db;
    padding-bottom: 6px;
    letter-spacing: 1.5px;
}

.sidebar a {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    color: #bdc3c7;
    text-decoration: none;
    font-size: 16px;
    margin-bottom: 8px;
    border-radius: 6px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.sidebar a i {
    margin-right: 12px;
    width: 18px;
    text-align: center;
}

.sidebar a:hover {
    background-color: #2980b9;
    color: #ecf0f1;
}

/* Content Styling */
.content {
    margin-left: 280px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.form-container {
    width: 100%;
    max-width: 1400px;
    background-color: #fff;
    padding: 30px 40px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0 auto;
}

.form-container h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 25px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    color: #2980b9;
    text-align: center;
}

.alert {
    padding: 15px;
    background-color: #d9edf7;
    border: 1px solid #bce8f1;
    border-radius: 5px;
    color: #31708f;
    margin-bottom: 25px;
    text-align: center;
}

.form-section {
    display: flex;
    gap: 30px;
    align-items: flex-start;
}

.form-left {
    flex: 0 0 400px;
    max-width: 400px;
}

.form-right {
    flex: 1;
    min-width: 600px;
}

.form-left h4 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 8px;
}

.form-right h4 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 8px;
}

/* Form Styling */
form {
    margin-bottom: 20px;
}

label {
    font-weight: 700;
    margin-bottom: 6px;
    display: block;
    color: #34495e;
}

select {
    width: 100%;
    padding: 10px 14px;
    font-size: 16px;
    border: 1.5px solid #ccc;
    border-radius: 6px;
    transition: border-color 0.3s ease;
    margin-bottom: 15px;
}

select:focus {
    border-color: #2980b9;
    outline: none;
}

button[type="submit"] {
    width: 100%;
    padding: 12px 0;
    background-color: #2980b9;
    border: none;
    color: #ecf0f1;
    font-size: 16px;
    font-weight: 700;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-top: 10px;
}

button[type="submit"]:hover {
    background-color: #1a5d8f;
}

/* Table Styling */
.assignment-list {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 25px;
    max-height: 600px;
    overflow-y: auto;
}

.table {
    margin-bottom: 0;
    font-size: 15px;
}

.table th {
    background-color: #2c3e50;
    color: white;
    font-weight: 600;
    border: none;
    padding: 12px 15px;
    font-size: 16px;
}

.table td {
    vertical-align: middle;
    padding: 10px 15px;
    border-bottom: 1px solid #dee2e6;
}

/* All subject headers use the same color */
.table-primary td,
.subject-bahasa-inggeris td,
.subject-bahasa-melayu td,
.subject-sains td,
.subject-sejarah td,
.subject-header td {
    background-color: rgb(108, 165, 223) !important;
    color: white !important;
    font-weight: bold;
}

/* Action buttons styling */
.btn {
    padding: 4px 8px;
    margin: 0 2px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    text-decoration: none;
    display: inline-block;
}

.btn-danger {
    background-color: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background-color: #c0392b;
}

.btn-sm {
    font-size: 11px;
    padding: 3px 6px;
}

/* Filter section styling */
.filter-section {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
}

/* Hidden rows for filtering */
.hidden-row {
    display: none !important;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
        box-shadow: none;
    }
    .content {
        margin-left: 0;
        padding: 20px;
    }
    .form-section {
        flex-direction: column;
    }
    .btn {
        font-size: 10px;
        padding: 2px 4px;
        margin: 1px;
    }
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
    <div class="form-container">
        <h2>Tetapan Guru-Subjek-Kelas</h2>

        <?php if ($message): ?>
            <div class="alert"><?= htmlspecialchars($message) ?></div>
        <?php endif; ?>

        <div class="form-section">
            <!-- Form section -->
            <div class="form-left">
                <h4>Tetapkan Guru kepada Subjek dan Kelas</h4>
                <form method="POST" action="">
                    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token']) ?>">
                    <input type="hidden" name="action" value="assign">

                    <label for="teacher_id">Guru</label>
                    <select name="teacher_id" id="teacher_id" required>
                        <option value="">-- Pilih Guru --</option>
                        <?php foreach ($teachers as $t): ?>
                            <option value="<?= (int)$t['teacher_id'] ?>" <?= ($selected_teacher == $t['teacher_id']) ? 'selected' : '' ?>>
                                <?= htmlspecialchars($t['full_name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>

                    <label for="subject_id">Subjek</label>
                    <select name="subject_id" id="subject_id" required>
                        <option value="">-- Pilih Subjek --</option>
                        <?php foreach ($subjects as $s): ?>
                            <option value="<?= (int)$s['subject_id'] ?>" <?= ($selected_subject == $s['subject_id']) ? 'selected' : '' ?>>
                                <?= htmlspecialchars($s['subject_name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>

                    <label for="classroom_id">Kelas</label>
                    <select name="classroom_id" id="classroom_id" required>
                        <option value="">-- Pilih Kelas --</option>
                        <?php foreach ($classrooms as $c): ?>
                            <option value="<?= (int)$c['classroom_id'] ?>" <?= ($selected_classroom == $c['classroom_id']) ? 'selected' : '' ?>>
                                <?= htmlspecialchars($c['class_name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>

                    <button type="submit">Tetapkan Guru</button>
                </form>
            </div>

            <!-- Assignment list -->
            <div class="form-right">
                <div class="assignment-list">
                    <h4>Senarai Guru-Subjek-Kelas (Mengikut Subjek)</h4>

                    <!-- Subject Filter -->
                    <div class="filter-section" style="margin-bottom: 20px; background-color: #f8f9fa; padding: 15px; border-radius: 5px; border: 1px solid #dee2e6;">
                        <label for="subjectFilter" style="font-weight: bold; color: #2980b9;">
                            <i class="fas fa-search"></i> Cari Subjek:
                        </label>
                        <div style="position: relative; margin-top: 5px;">
                            <input type="text" id="subjectFilter" placeholder="Taip nama subjek untuk menapis..."
                                   style="width: 100%; padding: 8px 35px 8px 8px; border: 2px solid #3498db; border-radius: 4px;">
                            <button type="button" id="clearFilter" style="position: absolute; right: 8px; top: 50%; transform: translateY(-50%); background: none; border: none; color: #666; cursor: pointer; display: none;">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <small style="color: #666;">Carian tidak sensitif kepada huruf besar/kecil. <span id="filterResults"></span></small>
                    </div>

                    <table class="table table-bordered table-striped" id="assignmentTable">
                        <thead>
                            <tr>
                                <th>Subjek</th>
                                <th>Kelas</th>
                                <th>Guru</th>
                                <th style="width: 100px;">Tindakan</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($grouped)): ?>
                                <?php foreach ($grouped as $subject => $assignments): ?>
                                    <tr class="subject-header" data-subject="<?= strtolower(htmlspecialchars($subject)) ?>">
                                        <td colspan="4" style="background-color: rgb(108, 165, 223) !important; color: white !important; font-weight: bold;">
                                            <?= htmlspecialchars($subject) ?>
                                        </td>
                                    </tr>
                                    <?php foreach ($assignments as $assign): ?>
                                        <tr class="assignment-row" data-subject="<?= strtolower(htmlspecialchars($subject)) ?>">
                                            <td></td> <!-- Empty to keep subject row separate -->
                                            <td><?= htmlspecialchars($assign['class']) ?></td>
                                            <td><?= htmlspecialchars($assign['teacher']) ?></td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-danger delete-btn"
                                                        onclick="deleteAssignment(<?= $assign['tsc_id'] ?>, <?= json_encode($assign['teacher']) ?>, <?= json_encode($assign['class']) ?>, <?= json_encode($subject) ?>)">
                                                    <i class="fas fa-trash"></i> Padam
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr><td colspan="4" style="text-align: center; color: #7f8c8d; font-style: italic;">Tiada tetapan lagi.</td></tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Form (hidden) -->
<form id="deleteForm" method="POST" style="display: none;">
    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token']) ?>">
    <input type="hidden" name="action" value="delete">
    <input type="hidden" name="tsc_id" id="delete_tsc_id">
</form>

<script>
// Subject filter functionality
document.addEventListener('DOMContentLoaded', function() {
    const filterInput = document.getElementById('subjectFilter');
    const clearButton = document.getElementById('clearFilter');
    const filterResults = document.getElementById('filterResults');
    const table = document.getElementById('assignmentTable');

    function performFilter() {
        const filterValue = filterInput.value.toLowerCase().trim();
        const rows = table.querySelectorAll('tbody tr');
        let visibleSubjects = 0;
        let totalAssignments = 0;

        if (filterValue === '') {
            // Show all rows
            rows.forEach(row => {
                row.style.display = '';
            });
            clearButton.style.display = 'none';
            filterResults.textContent = '';
        } else {
            // Filter rows
            let visibleAssignments = 0;
            clearButton.style.display = 'inline';

            rows.forEach(row => {
                if (row.classList.contains('subject-header')) {
                    // This is a subject header
                    const rowSubject = row.getAttribute('data-subject');
                    // Initially hide, will show if has visible assignments
                    row.style.display = 'none';
                } else if (row.classList.contains('assignment-row')) {
                    // This is an assignment row
                    const rowSubject = row.getAttribute('data-subject');
                    totalAssignments++;

                    if (rowSubject.includes(filterValue)) {
                        row.style.display = '';
                        visibleAssignments++;

                        // Show the corresponding subject header
                        const subjectHeader = table.querySelector(`tr.subject-header[data-subject="${rowSubject}"]`);
                        if (subjectHeader && subjectHeader.style.display === 'none') {
                            subjectHeader.style.display = '';
                            visibleSubjects++;
                        }
                    } else {
                        row.style.display = 'none';
                    }
                }
            });

            filterResults.textContent = `Menunjukkan ${visibleAssignments} daripada ${totalAssignments} tetapan dalam ${visibleSubjects} subjek.`;
        }
    }

    filterInput.addEventListener('input', performFilter);

    clearButton.addEventListener('click', function() {
        filterInput.value = '';
        performFilter();
        filterInput.focus();
    });
});

// Delete assignment function
function deleteAssignment(tscId, teacherName, className, subjectName) {
    try {
        const confirmMessage = `Adakah anda pasti ingin memadam tetapan ini?\n\nGuru: ${teacherName}\nKelas: ${className}\nSubjek: ${subjectName}\n\nTindakan ini tidak boleh dibatalkan.`;

        if (confirm(confirmMessage)) {
            // Show loading state
            const deleteButtons = document.querySelectorAll('.delete-btn');
            deleteButtons.forEach(btn => {
                btn.disabled = true;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Memproses...';
            });

            document.getElementById('delete_tsc_id').value = tscId;
            document.getElementById('deleteForm').submit();
        }
    } catch (error) {
        console.error('Error deleting assignment:', error);
        alert('Ralat memadam tetapan. Sila cuba lagi.');
    }
}
</script>

</body>
</html>
