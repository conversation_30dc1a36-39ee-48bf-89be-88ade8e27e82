<?php
session_start();

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Teacher') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';

$teacher_user_id = $_SESSION['user_id'];

// Get teacher ID
$stmt = $conn->prepare("SELECT teacher_id, full_name, staff_id FROM teachers WHERE user_id = ?");
$stmt->bind_param("i", $teacher_user_id);
$stmt->execute();
$teacher = $stmt->get_result()->fetch_assoc();
$teacher_id = $teacher['teacher_id'] ?? 0;
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Teacher Assignment - SMKTMI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>

<style>
.content {
    margin-left: 280px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.debug-section {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.debug-section h4 {
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

.table th {
    background-color: #3498db;
    color: white;
}

.alert-debug {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #495057;
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
    <h2><i class="fas fa-bug"></i> Debug Teacher Assignment</h2>
    
    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i> <strong>Nota:</strong> Halaman ini untuk mendiagnosis masalah tetapan guru-kelas. Sila hubungi pentadbir jika terdapat masalah.
    </div>

    <!-- Teacher Information -->
    <div class="debug-section">
        <h4>1. Maklumat Guru</h4>
        <?php if ($teacher): ?>
            <table class="table table-bordered">
                <tr><th>User ID</th><td><?php echo $teacher_user_id; ?></td></tr>
                <tr><th>Teacher ID</th><td><?php echo $teacher_id; ?></td></tr>
                <tr><th>Nama Penuh</th><td><?php echo htmlspecialchars($teacher['full_name']); ?></td></tr>
                <tr><th>Staff ID</th><td><?php echo htmlspecialchars($teacher['staff_id']); ?></td></tr>
            </table>
        <?php else: ?>
            <div class="alert alert-danger">❌ Maklumat guru tidak dijumpai dalam database!</div>
        <?php endif; ?>
    </div>

    <!-- Classroom Assignments -->
    <div class="debug-section">
        <h4>2. Tetapan Kelas</h4>
        <?php
        $classroomStmt = $conn->prepare("
            SELECT c.classroom_id, c.class_name, c.tingkatan, c.teacher_id
            FROM classrooms c
            WHERE c.teacher_id = ?
        ");
        $classroomStmt->bind_param("i", $teacher_id);
        $classroomStmt->execute();
        $classrooms = $classroomStmt->get_result();
        ?>
        
        <?php if ($classrooms->num_rows > 0): ?>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>Classroom ID</th>
                        <th>Nama Kelas</th>
                        <th>Tingkatan</th>
                        <th>Teacher ID</th>
                    </tr>
                </thead>
                <tbody>
                    <?php while ($classroom = $classrooms->fetch_assoc()): ?>
                    <tr>
                        <td><?php echo $classroom['classroom_id']; ?></td>
                        <td><?php echo htmlspecialchars($classroom['class_name']); ?></td>
                        <td><?php echo $classroom['tingkatan']; ?></td>
                        <td><?php echo $classroom['teacher_id']; ?></td>
                    </tr>
                    <?php endwhile; ?>
                </tbody>
            </table>
        <?php else: ?>
            <div class="alert alert-warning">⚠️ Tiada kelas ditetapkan kepada guru ini.</div>
        <?php endif; ?>
    </div>

    <!-- Students in Teacher's Classes -->
    <div class="debug-section">
        <h4>3. Pelajar dalam Kelas</h4>
        <?php
        $studentsStmt = $conn->prepare("
            SELECT s.student_id, s.full_name, s.no_ic, c.class_name, c.classroom_id
            FROM students s
            JOIN classrooms c ON s.classroom_id = c.classroom_id
            WHERE c.teacher_id = ?
            ORDER BY c.class_name, s.full_name
        ");
        $studentsStmt->bind_param("i", $teacher_id);
        $studentsStmt->execute();
        $students = $studentsStmt->get_result();
        ?>
        
        <?php
        $students_count = $students->num_rows;
        if ($students_count > 0): ?>
            <div class="alert alert-success">✅ Dijumpai <?php echo $students_count; ?> pelajar dalam kelas anda.</div>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>Student ID</th>
                        <th>Nama Pelajar</th>
                        <th>No. IC</th>
                        <th>Kelas</th>
                        <th>Classroom ID</th>
                    </tr>
                </thead>
                <tbody>
                    <?php while ($student = $students->fetch_assoc()): ?>
                    <tr>
                        <td><?php echo $student['student_id']; ?></td>
                        <td><?php echo htmlspecialchars($student['full_name']); ?></td>
                        <td><?php echo htmlspecialchars($student['no_ic']); ?></td>
                        <td><?php echo htmlspecialchars($student['class_name']); ?></td>
                        <td><?php echo $student['classroom_id']; ?></td>
                    </tr>
                    <?php endwhile; ?>
                </tbody>
            </table>
        <?php else: ?>
            <div class="alert alert-warning">⚠️ Tiada pelajar dijumpai dalam kelas yang ditetapkan kepada anda.</div>
        <?php endif; ?>
    </div>

    <!-- All Classrooms (for reference) -->
    <div class="debug-section">
        <h4>4. Semua Kelas dalam Sistem</h4>
        <?php
        $allClassrooms = $conn->query("
            SELECT c.classroom_id, c.class_name, c.tingkatan, c.teacher_id, t.full_name as teacher_name
            FROM classrooms c
            LEFT JOIN teachers t ON c.teacher_id = t.teacher_id
            ORDER BY c.tingkatan, c.class_name
        ");
        ?>
        
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>Classroom ID</th>
                    <th>Nama Kelas</th>
                    <th>Tingkatan</th>
                    <th>Teacher ID</th>
                    <th>Nama Guru</th>
                </tr>
            </thead>
            <tbody>
                <?php while ($classroom = $allClassrooms->fetch_assoc()): ?>
                <tr class="<?php echo ($classroom['teacher_id'] == $teacher_id) ? 'table-success' : ''; ?>">
                    <td><?php echo $classroom['classroom_id']; ?></td>
                    <td><?php echo htmlspecialchars($classroom['class_name']); ?></td>
                    <td><?php echo $classroom['tingkatan']; ?></td>
                    <td><?php echo $classroom['teacher_id'] ?? 'NULL'; ?></td>
                    <td><?php echo htmlspecialchars($classroom['teacher_name'] ?? 'Tiada Guru'); ?></td>
                </tr>
                <?php endwhile; ?>
            </tbody>
        </table>
        <small class="text-muted">Baris berwarna hijau menunjukkan kelas yang ditetapkan kepada anda.</small>
    </div>

    <!-- Recommendations -->
    <div class="debug-section">
        <h4>5. Cadangan Penyelesaian</h4>
        <div class="alert alert-debug">
            <h6>Jika tiada pelajar dijumpai:</h6>
            <ol>
                <li><strong>Semak tetapan guru kelas:</strong> Pastikan anda telah ditetapkan sebagai guru kelas oleh pentadbir</li>
                <li><strong>Semak pendaftaran pelajar:</strong> Pastikan pelajar telah didaftarkan dalam kelas anda</li>
                <li><strong>Hubungi pentadbir:</strong> Jika masalah berterusan, hubungi pentadbir sistem</li>
            </ol>
        </div>
    </div>

    <div class="text-center mt-4">
        <a href="discipline_report.php" class="btn btn-primary">
            <i class="fas fa-arrow-left"></i> Kembali ke Laporan Disiplin
        </a>
        <a href="dashboard.php" class="btn btn-secondary">
            <i class="fas fa-home"></i> Dashboard
        </a>
    </div>
</div>

</body>
</html>
