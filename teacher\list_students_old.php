<?php
session_start();

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Teacher') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';

$userId = $_SESSION['user_id'];
$message = '';

// Handle PIBG checkbox updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['student_id']) && isset($_POST['pibg_paid'])) {
    $student_id = intval($_POST['student_id']);
    $pibg_paid = intval($_POST['pibg_paid']);

    try {
        $updateStmt = $conn->prepare("UPDATE students SET pibg_paid = ? WHERE student_id = ?");
        $updateStmt->bind_param("ii", $pibg_paid, $student_id);
        if ($updateStmt->execute()) {
            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['success' => false, 'error' => 'Database update failed']);
        }
        $updateStmt->close();
        exit;
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        exit;
    }
}

// Get teacher_id from teachers table using user_id
$teacherId = null;
try {
    $stmt = $conn->prepare("SELECT teacher_id, full_name FROM teachers WHERE user_id = ?");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    $teacher = $result->fetch_assoc();
    $teacherId = $teacher['teacher_id'] ?? null;
    $stmt->close();

    if (!$teacherId) {
        $message = "❌ Ralat: Maklumat guru tidak dijumpai. Sila hubungi pentadbir.";
    }
} catch (Exception $e) {
    $message = "❌ Ralat mengambil maklumat guru: " . $e->getMessage();
}

// Add PIBG column to students table if it doesn't exist
try {
    $result = $conn->query("SHOW COLUMNS FROM students LIKE 'pibg_paid'");
    if ($result->num_rows == 0) {
        $conn->query("ALTER TABLE students ADD COLUMN pibg_paid TINYINT(1) DEFAULT 0 AFTER parent_phone");
    }
} catch (Exception $e) {
    // Column might already exist, continue
}

// Handle PIBG payment update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_pibg'])) {
    $student_id = (int)$_POST['student_id'];
    $pibg_paid = isset($_POST['pibg_paid']) ? 1 : 0;
    
    try {
        $stmt = $conn->prepare("UPDATE students SET pibg_paid = ? WHERE student_id = ?");
        $stmt->bind_param("ii", $pibg_paid, $student_id);
        
        if ($stmt->execute()) {
            $message = "✅ Status PIBG telah dikemaskini.";
        } else {
            $message = "❌ Ralat semasa mengemaskini status PIBG.";
        }
        $stmt->close();
    } catch (Exception $e) {
        $message = "❌ Ralat: " . $e->getMessage();
    }
}

// Get teacher's own classrooms (classes where teacher is the class teacher/wali kelas)
$teacher_classrooms = [];
try {
    $stmt = $conn->prepare("
        SELECT c.classroom_id, c.class_name
        FROM classrooms c
        WHERE c.teacher_id = ?
        ORDER BY c.class_name
    ");
    $stmt->bind_param("i", $teacherId);
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $teacher_classrooms[] = $row;
    }
    $stmt->close();
} catch (Exception $e) {
    $message = "❌ Ralat mengambil senarai kelas: " . $e->getMessage();
}

// Debug: Add some debugging information
if (empty($teacher_classrooms)) {
    $message .= " | Debug: User ID = $userId, Teacher ID = $teacherId, No classrooms found where teacher_id = $teacherId";
}

// Get selected classroom
$selected_classroom = isset($_GET['classroom_id']) ? (int)$_GET['classroom_id'] : 
    (!empty($teacher_classrooms) ? $teacher_classrooms[0]['classroom_id'] : 0);

// Verify that selected classroom belongs to this teacher
$classroom_valid = false;
foreach ($teacher_classrooms as $classroom) {
    if ($classroom['classroom_id'] == $selected_classroom) {
        $classroom_valid = true;
        break;
    }
}

if (!$classroom_valid && !empty($teacher_classrooms)) {
    $selected_classroom = $teacher_classrooms[0]['classroom_id'];
}

// Get students in selected classroom
$students = [];
$classroom_name = '';
if ($selected_classroom > 0 && $classroom_valid) {
    try {
        // Get classroom name
        $stmt = $conn->prepare("SELECT class_name FROM classrooms WHERE classroom_id = ?");
        $stmt->bind_param("i", $selected_classroom);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($row = $result->fetch_assoc()) {
            $classroom_name = $row['class_name'];
        }
        $stmt->close();
        
        // Get students in this specific classroom with parent information
        $stmt = $conn->prepare("
            SELECT s.student_id, s.full_name, s.no_ic, s.pibg_paid,
                   p.full_name as parent_name, p.phone_number as parent_phone
            FROM students s
            LEFT JOIN parents p ON s.parent_id = p.parent_id
            WHERE s.classroom_id = ?
            ORDER BY s.full_name
        ");
        $stmt->bind_param("i", $selected_classroom);
        $stmt->execute();
        $result = $stmt->get_result();
        while ($row = $result->fetch_assoc()) {
            $students[] = $row;
        }
        $stmt->close();
    } catch (Exception $e) {
        $message = "❌ Ralat mengambil senarai pelajar: " . $e->getMessage();
    }

    // Debug: Add debugging for student count
    if (empty($students) && $selected_classroom > 0) {
        $message .= " | Debug: Selected classroom = $selected_classroom, Found " . count($students) . " students";
    }
}

// Calculate PIBG statistics
$pibg_stats = [
    'total' => count($students),
    'paid' => 0,
    'unpaid' => 0
];

foreach ($students as $student) {
    if ($student['pibg_paid']) {
        $pibg_stats['paid']++;
    } else {
        $pibg_stats['unpaid']++;
    }
}
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Senarai Pelajar - SMKTMI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>

<style>
/* Content Styling */
.content {
    margin-left: 320px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.form-container {
    width: 100%;
    max-width: 1400px;
    background-color: #fff;
    padding: 30px 40px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0 auto;
}

.form-container h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 25px;
    border-bottom: 3px solid rgb(108, 165, 223);
    padding-bottom: 10px;
    color: #2980b9;
    text-align: center;
}

.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, rgb(108, 165, 223) 0%, #2980b9 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.stat-card.paid {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
}

.stat-card.unpaid {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.table th {
    background-color: rgb(153, 156, 158);
    color: white;
    text-align: center;
    font-weight: 600;
}

.table td {
    vertical-align: middle;
}

.pibg-checkbox {
    transform: scale(1.3);
    margin: 0;
    cursor: pointer;
}

.pibg-form {
    display: inline-block;
    margin: 0;
}

.pibg-cell {
    text-align: center;
    vertical-align: middle;
    background-color: #f8f9fa;
}

.pibg-cell.paid {
    background-color: #d4edda;
}

.card-header {
    background-color: rgb(108, 165, 223);
    color: white;
    font-weight: 600;
}

.table th {
    background-color: rgb(153, 156, 158);
    color: white;
    text-align: center;
    font-weight: 600;
    border: none;
}

.table td {
    vertical-align: middle;
    border-color: #dee2e6;
}

/* Responsive */
@media (max-width: 768px) {
    .content {
        margin-left: 0;
        padding: 20px;
    }
    
    .table-responsive {
        font-size: 14px;
    }
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
  <div class="form-container">
    <h2><i class="fas fa-users"></i> Senarai Pelajar Telah Didaftarkan <?php echo $classroom_name ? "- $classroom_name" : ""; ?></h2>

    <?php if ($message): ?>
      <div class="alert <?php echo strpos($message, '✅') !== false ? 'alert-success' : 'alert-danger'; ?>">
        <?php echo $message; ?>
      </div>
    <?php endif; ?>

    <!-- Class Information -->
    <?php if (!empty($teacher_classrooms)): ?>
    <div class="row mb-4">
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h5><i class="fas fa-chalkboard"></i> Maklumat Kelas</h5>
          </div>
          <div class="card-body">
            <label class="form-label"><strong>Kelas Anda:</strong></label>
            <?php if (count($teacher_classrooms) > 1): ?>
            <select class="form-select" onchange="window.location.href='?classroom_id=' + this.value">
              <?php foreach ($teacher_classrooms as $classroom): ?>
                <option value="<?php echo $classroom['classroom_id']; ?>"
                        <?php echo $classroom['classroom_id'] == $selected_classroom ? 'selected' : ''; ?>>
                  <?php echo htmlspecialchars($classroom['class_name']); ?>
                </option>
              <?php endforeach; ?>
            </select>
            <?php else: ?>
            <div class="form-control-plaintext">
              <strong><?php echo htmlspecialchars($classroom_name); ?></strong>
            </div>
            <?php endif; ?>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h5><i class="fas fa-info-circle"></i> Ringkasan</h5>
          </div>
          <div class="card-body">
            <p><strong>Jumlah Pelajar:</strong> <span class="total-students"><?php echo $pibg_stats['total']; ?> orang</span></p>
            <p><strong>PIBG Sudah Bayar:</strong> <span class="paid-students"><?php echo $pibg_stats['paid']; ?> orang</span></p>
            <p><strong>PIBG Belum Bayar:</strong> <span class="unpaid-students"><?php echo $pibg_stats['unpaid']; ?> orang</span></p>
          </div>
        </div>
      </div>
    </div>

    <!-- Student List Section -->
    <?php if (!empty($students)): ?>
    <div class="card">
      <div class="card-header">
        <h5><i class="fas fa-list"></i> Senarai Pelajar Telah Didaftarkan - <?php echo htmlspecialchars($classroom_name); ?></h5>
      </div>
      <div class="card-body p-0">

      <!-- Students Table -->
      <div class="table-responsive">
        <table class="table table-hover mb-0">
          <thead>
            <tr>
              <th style="width: 60px; text-align: center;">No.</th>
              <th style="text-align: center;">Nama Pelajar</th>
              <th style="width: 140px; text-align: center;">No Kad Pengenalan</th>
              <th style="text-align: center;">Nama Ibu/Bapa</th>
              <th style="width: 140px; text-align: center;">No Telefon Ibu/Bapa</th>
              <th style="width: 100px; text-align: center;">PIBG</th>
            </tr>
          </thead>
        <tbody>
          <?php $bil = 1; foreach ($students as $student): ?>
          <tr>
            <td class="text-center"><?php echo $bil++; ?></td>
            <td class="text-center"><strong><?php echo htmlspecialchars($student['full_name']); ?></strong></td>
            <td class="text-center"><?php echo htmlspecialchars($student['no_ic'] ?: '-'); ?></td>
            <td class="text-center"><?php echo htmlspecialchars($student['parent_name'] ?: '-'); ?></td>
            <td class="text-center"><?php echo htmlspecialchars($student['parent_phone'] ?: '-'); ?></td>
            <td class="text-center pibg-cell <?php echo $student['pibg_paid'] ? 'paid' : ''; ?>">
              <div class="pibg-container">
                <input type="checkbox"
                       class="pibg-checkbox"
                       <?php echo $student['pibg_paid'] ? 'checked' : ''; ?>
                       data-student-id="<?php echo $student['student_id']; ?>"
                       onchange="updatePIBG(this)"
                       title="<?php echo $student['pibg_paid'] ? 'PIBG Sudah Bayar' : 'PIBG Belum Bayar'; ?>">
                <br><small class="pibg-status"><?php echo $student['pibg_paid'] ? 'Sudah' : 'Belum'; ?></small>
              </div>
            </td>
          </tr>
          <?php endforeach; ?>
        </tbody>
      </table>
      </div>
    </div>
    <?php else: ?>
    <div class="text-center" style="padding: 40px;">
      <i class="fas fa-users" style="font-size: 48px; color: #bdc3c7; margin-bottom: 15px;"></i>
      <h5 style="color: #7f8c8d;">Tiada Pelajar</h5>
      <p style="color: #95a5a6;">
        <?php if (empty($teacher_classrooms)): ?>
          Anda belum ditugaskan ke mana-mana kelas.
        <?php else: ?>
          Tiada pelajar dalam kelas yang dipilih.
        <?php endif; ?>
      </p>
    </div>
    <?php endif; ?>
    <?php else: ?>
    <div class="text-center" style="padding: 40px;">
      <i class="fas fa-chalkboard-teacher" style="font-size: 48px; color: #bdc3c7; margin-bottom: 15px;"></i>
      <h5 style="color: #7f8c8d;">Tiada Kelas Ditugaskan</h5>
      <p style="color: #95a5a6;">Anda belum ditugaskan sebagai guru kelas. Sila hubungi pentadbir.</p>
    </div>
    <?php endif; ?>

    <!-- Information -->
    <div class="mt-4 p-3" style="background-color: #e3f2fd; border-radius: 6px; border-left: 4px solid #2196f3;">
      <h5><i class="fas fa-info-circle"></i> Maklumat PIBG</h5>
      <ul class="mb-0">
        <li><strong>PIBG:</strong> Persatuan Ibu Bapa dan Guru</li>
        <li><strong>Cara Guna:</strong> Klik checkbox untuk menanda pelajar yang sudah bayar PIBG</li>
        <li><strong>Statistik:</strong> Lihat jumlah pelajar yang sudah/belum bayar di bahagian atas</li>
      </ul>
    </div>
  </div>
</div>

<script>
function updatePIBG(checkbox) {
    const studentId = checkbox.getAttribute('data-student-id');
    const pibgPaid = checkbox.checked ? 1 : 0;
    const cell = checkbox.closest('.pibg-cell');
    const statusText = cell.querySelector('.pibg-status');

    // Show loading state
    checkbox.disabled = true;
    statusText.textContent = 'Updating...';

    // Create form data
    const formData = new FormData();
    formData.append('student_id', studentId);
    formData.append('pibg_paid', pibgPaid);

    // Send AJAX request
    fetch(window.location.href, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update UI
            if (pibgPaid) {
                cell.classList.add('paid');
                statusText.textContent = 'Sudah';
                checkbox.title = 'PIBG Sudah Bayar';
            } else {
                cell.classList.remove('paid');
                statusText.textContent = 'Belum';
                checkbox.title = 'PIBG Belum Bayar';
            }

            // Update statistics
            updateStatistics();
        } else {
            // Revert checkbox on error
            checkbox.checked = !checkbox.checked;
            alert('Ralat: ' + (data.error || 'Gagal mengemaskini status PIBG'));
        }
    })
    .catch(error => {
        // Revert checkbox on error
        checkbox.checked = !checkbox.checked;
        alert('Ralat rangkaian: ' + error.message);
    })
    .finally(() => {
        // Re-enable checkbox
        checkbox.disabled = false;
        if (statusText.textContent === 'Updating...') {
            statusText.textContent = checkbox.checked ? 'Sudah' : 'Belum';
        }
    });
}

function updateStatistics() {
    // Count checked and unchecked checkboxes
    const checkboxes = document.querySelectorAll('.pibg-checkbox');
    let paid = 0;
    let unpaid = 0;

    checkboxes.forEach(cb => {
        if (cb.checked) {
            paid++;
        } else {
            unpaid++;
        }
    });

    // Update the statistics display
    const totalElement = document.querySelector('.total-students');
    const paidElement = document.querySelector('.paid-students');
    const unpaidElement = document.querySelector('.unpaid-students');

    if (totalElement) totalElement.textContent = (paid + unpaid) + ' orang';
    if (paidElement) paidElement.textContent = paid + ' orang';
    if (unpaidElement) unpaidElement.textContent = unpaid + ' orang';
}
</script>

</body>
</html>
