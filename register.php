<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Pendaftaran Ibu / Bapa / Penjaga</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: #f0f2f5;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
    }

    .form-container {
      background: #fff;
      padding: 2rem;
      border-radius: 10px;
      box-shadow: 0 8px 24px rgba(0,0,0,0.1);
      width: 100%;
      max-width: 480px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    h2 {
      text-align: center;
      margin-bottom: 1.5rem;
      color: #333;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      font-size: 1.5rem;
      font-weight: 600;
    }

    .form-group {
      position: relative;
      margin-bottom: 1.2rem;
    }

    label {
      display: block;
      margin-bottom: 0.4rem;
      color: #444;
      font-size: 0.95rem;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      font-weight: 500;
    }

    input, textarea {
      width: 100%;
      padding: 8px 36px 8px 10px;
      font-size: 0.95rem;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      border: 1px solid #ccc;
      border-radius: 5px;
      transition: 0.3s ease;
      box-sizing: border-box;
    }

    textarea {
      resize: vertical;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    input:focus, textarea:focus {
      border-color: #007bff;
      outline: none;
    }

    .password-toggle {
      position: absolute;
      top: 33px;
      right: 10px;
      cursor: pointer;
      width: 18px;
      height: 18px;
      fill: #888;
    }

    .note {
      font-size: 0.85rem;
      color: #777;
      margin-top: 4px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .error {
      color: red;
      font-size: 0.85rem;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .btn {
      background-color: #007bff;
      color: white;
      padding: 10px;
      border: none;
      width: 100%;
      border-radius: 6px;
      font-size: 1rem;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      font-weight: 500;
      cursor: pointer;
      transition: background 0.3s ease;
    }

    .btn:hover {
      background-color: #0056b3;
    }
  </style>
</head>
<body>

<div class="form-container">
  <h2>Pendaftaran Ibu / Bapa / Penjaga</h2>
  <form method="post" action="register_process.php" onsubmit="return validateForm()" autocomplete="off">

    <!-- Hidden dummy inputs to block autofill -->
    <input type="text" name="fakeusernameremembered" style="display:none" autocomplete="off" />
    <input type="password" name="fakepasswordremembered" style="display:none" autocomplete="off" />

    <div class="form-group">
      <label>Nama Penuh</label>
      <input type="text" name="full_name" required autocomplete="off" />
    </div>

    <div class="form-group">
      <label>Emel</label>
      <input type="email" name="email" required autocomplete="off" />
    </div>

    <div class="form-group">
      <label>No Telefon</label>
      <input type="text" name="phone_number" required autocomplete="off" />
    </div>

    <div class="form-group">
      <label>Alamat</label>
      <textarea name="address" rows="3" required autocomplete="off"></textarea>
    </div>

    <div class="form-group">
      <label>Kata Laluan</label>
      <input type="password" name="password" id="password" required oninput="checkPasswordPolicy()" autocomplete="new-password" />
      <svg class="password-toggle" onclick="togglePassword('password', this)" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
        <path d="M12 5c-7 0-11 7-11 7s4 7 11 7 11-7 11-7-4-7-11-7zm0 12c-2.8 0-5-2.2-5-5s2.2-5 
        5-5 5 2.2 5 5-2.2 5-5 5zm0-8a3 3 0 100 6 3 3 0 000-6z"/>
      </svg>
      <div class="note">Minima 8 aksara dan mesti ada huruf besar, huruf kecil, nombor & simbol (cth: Contoh123!).</div>
      <div id="policy-error" class="error"></div>
    </div>

    <div class="form-group">
      <label>Ulang Kata Laluan</label>
      <input type="password" name="confirm_password" id="confirm_password" required autocomplete="new-password" />
      <svg class="password-toggle" onclick="togglePassword('confirm_password', this)" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
        <path d="M12 5c-7 0-11 7-11 7s4 7 11 7 11-7 11-7-4-7-11-7zm0 12c-2.8 0-5-2.2-5-5s2.2-5 
        5-5 5 2.2 5 5-2.2 5-5 5zm0-8a3 3 0 100 6 3 3 0 000-6z"/>
      </svg>
      <div id="match-error" class="error"></div>
    </div>

    <button type="submit" class="btn">Daftar</button>
  </form>
</div>

<script>
  function togglePassword(id, icon) {
    const field = document.getElementById(id);
    if (field.type === 'password') {
      field.type = 'text';
      icon.style.fill = '#007bff';
    } else {
      field.type = 'password';
      icon.style.fill = '#888';
    }
  }

  function checkPasswordPolicy() {
    const password = document.getElementById("password").value;
    const policyError = document.getElementById("policy-error");

    const policyRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$/;

    if (!policyRegex.test(password)) {
      policyError.textContent = "Kata laluan belum ikut polisi.";
    } else {
      policyError.textContent = "";
    }
  }

  function validateForm() {
    const pass = document.getElementById("password").value;
    const confirm = document.getElementById("confirm_password").value;
    const matchError = document.getElementById("match-error");
    const policyError = document.getElementById("policy-error");

    const policyRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$/;

    if (!policyRegex.test(pass)) {
      policyError.textContent = "Kata laluan tidak ikut polisi.";
      return false;
    }

    if (pass !== confirm) {
      matchError.textContent = "Kata laluan tidak sepadan.";
      return false;
    } else {
      matchError.textContent = "";
    }

    return true;
  }
</script>

</body>
</html>
