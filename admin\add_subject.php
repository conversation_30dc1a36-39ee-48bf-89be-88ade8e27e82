<?php
session_start();
require '../db.php';

include 'includes/header.php';

$subjectName = '';
$editMode = false;
$message = '';

// Tambah subjek
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($_POST['subject_name']) && empty($_POST['subject_id'])) {
    $subjectName = trim($_POST['subject_name']);
    $check = $conn->prepare("SELECT * FROM subjects WHERE subject_name = ?");
    $check->bind_param("s", $subjectName);
    $check->execute();
    $checkResult = $check->get_result();

    if ($checkResult->num_rows > 0) {
        $message = "❌ Subjek \"$subjectName\" telah wujud.";
    } else {
        $insert = $conn->prepare("INSERT INTO subjects (subject_name) VALUES (?)");
        $insert->bind_param("s", $subjectName);
        if ($insert->execute()) {
            $message = "✅ Subjek \"$subjectName\" berjaya ditambah.";
        } else {
            $message = "❌ Gagal tambah subjek: " . $conn->error;
        }
    }
}

// Padam subjek
if (isset($_GET['delete_id'])) {
    $delete_id = (int)$_GET['delete_id'];
    $delete = $conn->prepare("DELETE FROM subjects WHERE subject_id = ?");
    $delete->bind_param("i", $delete_id);
    if ($delete->execute()) {
        $message = "✅ Subjek berjaya dipadam.";
    } else {
        $message = "❌ Subjek tidak berjaya dipadam. Subjek digunakan di table lain.";
    }
}

// Sedia untuk kemaskini
if (isset($_GET['edit_id'])) {
    $edit_id = (int)$_GET['edit_id'];
    $edit = $conn->prepare("SELECT * FROM subjects WHERE subject_id = ?");
    $edit->bind_param("i", $edit_id);
    $edit->execute();
    $editResult = $edit->get_result();
    if ($editResult->num_rows > 0) {
        $subject = $editResult->fetch_assoc();
        $subjectName = $subject['subject_name'];
        $editMode = true;
    }
}

// Simpan kemaskini subjek
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($_POST['subject_id']) && !empty($_POST['subject_name'])) {
    $id = (int)$_POST['subject_id'];
    $subjectName = trim($_POST['subject_name']);

    $check = $conn->prepare("SELECT * FROM subjects WHERE subject_name = ? AND subject_id != ?");
    $check->bind_param("si", $subjectName, $id);
    $check->execute();
    $checkResult = $check->get_result();

    if ($checkResult->num_rows > 0) {
        $message = "❌ Nama subjek \"$subjectName\" telah digunakan.";
    } else {
        $update = $conn->prepare("UPDATE subjects SET subject_name = ? WHERE subject_id = ?");
        $update->bind_param("si", $subjectName, $id);
        if ($update->execute()) {
            $message = "✅ Subjek berjaya dikemaskini.";
            $editMode = false;
            $subjectName = '';
        } else {
            $message = "❌ Gagal kemaskini: " . $conn->error;
        }
    }
}

// Papar semua subjek
$subjects = $conn->query("SELECT * FROM subjects ORDER BY subject_name ASC");
?>

<!-- FontAwesome CDN for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<style>
/* Sidebar Styling */
.sidebar {
    position: fixed;
    top: 0; left: 0;
    width: 300px;
    height: 100vh;
    background-color: #2c3e50;
    padding: 30px 20px;
    color: #ecf0f1;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow-y: auto;
    box-shadow: 5px 0 15px rgba(0,0,0,0.4);
    border-right: 1px solid #34495e;
}

.sidebar h4 {
    font-size: 18px;
    font-weight: 700;
    text-transform: uppercase;
    margin: 30px 0 12px;
    border-bottom: 2px solid #3498db;
    padding-bottom: 6px;
    letter-spacing: 1.5px;
}

.sidebar a {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    color: #bdc3c7;
    text-decoration: none;
    font-size: 16px;
    margin-bottom: 8px;
    border-radius: 6px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.sidebar a i {
    margin-right: 12px;
    width: 18px;
    text-align: center;
}

.sidebar a:hover {
    background-color: #2980b9;
    color: #ecf0f1;
}

/* Content Styling */
.content {
    margin-left: 280px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.form-container {
    width: 100%;
    max-width: 1200px;
    background-color: #fff;
    padding: 30px 40px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0 auto;
}

.form-container h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 25px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    color: #2980b9;
    text-align: center;
}

.alert {
    padding: 15px;
    background-color: #d9edf7;
    border: 1px solid #bce8f1;
    border-radius: 5px;
    color: #31708f;
    margin-bottom: 25px;
    text-align: center;
}

.form-section {
    display: flex;
    gap: 30px;
    align-items: flex-start;
}

.form-left {
    flex: 1;
    max-width: 400px;
}

.form-right {
    flex: 2;
}

.form-left h5 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 8px;
}

/* Form Styling */
form {
    margin-bottom: 20px;
}

label {
    font-weight: 700;
    margin-bottom: 6px;
    display: block;
    color: #34495e;
}

input[type="text"] {
    width: 100%;
    padding: 10px 14px;
    font-size: 16px;
    border: 1.5px solid #ccc;
    border-radius: 6px;
    transition: border-color 0.3s ease;
    margin-bottom: 15px;
}

input:focus {
    border-color: #2980b9;
    outline: none;
}

button[type="submit"] {
    padding: 10px 20px;
    background-color: #2980b9;
    border: none;
    color: #ecf0f1;
    font-size: 16px;
    font-weight: 700;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-right: 10px;
}

button[type="submit"]:hover {
    background-color: #1a5d8f;
}

.btn-secondary {
    padding: 10px 20px;
    background-color: #95a5a6;
    border: none;
    color: #ecf0f1;
    font-size: 16px;
    font-weight: 700;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
    text-decoration: none;
    color: #ecf0f1;
}

/* Table Styling */
.subject-list {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 20px;
    max-height: 500px;
    overflow-y: auto;
}

.subject-list h5 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 8px;
}

.table {
    margin-bottom: 0;
}

.table th {
    background-color: #2c3e50;
    color: white;
    font-weight: 600;
    border: none;
}

.table td {
    vertical-align: middle;
}

.btn-sm {
    padding: 5px 10px;
    font-size: 12px;
    border-radius: 4px;
    margin-right: 5px;
}

.btn-primary {
    background-color: #3498db;
    border: none;
    color: white;
    text-decoration: none;
}

.btn-primary:hover {
    background-color: #2980b9;
    text-decoration: none;
    color: white;
}

.btn-danger {
    background-color: #e74c3c;
    border: none;
    color: white;
    text-decoration: none;
}

.btn-danger:hover {
    background-color: #c0392b;
    text-decoration: none;
    color: white;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
        box-shadow: none;
    }
    .content {
        margin-left: 0;
        padding: 20px;
    }
    .form-section {
        flex-direction: column;
    }
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
    <div class="form-container">
        <h2>Pengurusan Subjek</h2>

        <?php if (!empty($message)): ?>
            <div class="alert"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>

        <div class="form-section">
            <!-- Form section -->
            <div class="form-left">
                <h5><?php echo $editMode ? 'Kemaskini Subjek' : 'Tambah Subjek'; ?></h5>
                <form method="POST" action="">
                    <?php if ($editMode): ?>
                        <input type="hidden" name="subject_id" value="<?php echo htmlspecialchars($subject['subject_id']); ?>">
                    <?php endif; ?>
                    <label for="subject_name">Nama Subjek</label>
                    <input type="text" name="subject_name" id="subject_name" required value="<?php echo htmlspecialchars($subjectName); ?>">
                    <button type="submit">
                        <?php echo $editMode ? 'Kemaskini' : 'Tambah'; ?>
                    </button>
                    <?php if ($editMode): ?>
                        <a href="add_subject.php" class="btn-secondary">Batal</a>
                    <?php endif; ?>
                </form>
            </div>

            <!-- Subject list -->
            <div class="form-right">
                <div class="subject-list">
                    <h5>Senarai Subjek</h5>
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th style="width: 60px;">Bil.</th>
                                <th>Nama Subjek</th>
                                <th style="width: 150px;">Tindakan</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if ($subjects->num_rows > 0): ?>
                                <?php $bil = 1; while ($row = $subjects->fetch_assoc()): ?>
                                    <tr>
                                        <td><?php echo $bil++; ?></td>
                                        <td><?php echo htmlspecialchars($row['subject_name']); ?></td>
                                        <td>
                                            <a href="?edit_id=<?php echo $row['subject_id']; ?>" class="btn-sm btn-primary">Edit</a>
                                            <a href="?delete_id=<?php echo $row['subject_id']; ?>" class="btn-sm btn-danger" onclick="return confirm('Padam subjek ini?');">Padam</a>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <tr><td colspan="3" style="text-align: center; color: #7f8c8d; font-style: italic;">Tiada subjek direkodkan.</td></tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
