<?php
session_start();

// Check authentication
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Teacher') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';
require '../includes/universal_logger.php';

$teacher_user_id = $_SESSION['user_id'];

// Get teacher information
$teacherStmt = $conn->prepare("SELECT teacher_id, full_name FROM teachers WHERE user_id = ?");
$teacherStmt->bind_param("i", $teacher_user_id);
$teacherStmt->execute();
$teacherResult = $teacherStmt->get_result();
$teacher = $teacherResult->fetch_assoc();

if (!$teacher) {
    die("Error: Teacher not found");
}

$teacher_id = $teacher['teacher_id'];
$teacher_name = $teacher['full_name'];

// Initialize variables
$message = "";
$success = false;

// Ensure table structure has all required columns
function ensureTableStructure($conn) {
    try {
        // Get existing columns
        $columns = $conn->query("SHOW COLUMNS FROM discipline_incidents");
        if (!$columns) {
            return false; // Table doesn't exist
        }

        $existing_columns = [];
        while ($col = $columns->fetch_assoc()) {
            $existing_columns[] = $col['Field'];
        }

        // Define required columns with their definitions
        $required_columns = [
            'action_taken' => "text",
            'severity' => "enum('Ringan','Sederhana','Berat') DEFAULT 'Ringan'",
            'status' => "enum('Baru','Dalam Tindakan','Selesai') DEFAULT 'Baru'",
            'updated_at' => "timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
        ];

        // Add missing columns
        foreach ($required_columns as $column => $definition) {
            if (!in_array($column, $existing_columns)) {
                $alterSQL = "ALTER TABLE discipline_incidents ADD COLUMN $column $definition";
                $conn->query($alterSQL);
            }
        }

        return true;
    } catch (Exception $e) {
        return false;
    }
}

// Ensure table structure is correct
ensureTableStructure($conn);

// Handle success redirect
if (isset($_GET['success']) && $_GET['success'] == '1') {
    $student_name = $_GET['student'] ?? 'pelajar';
    $incident_id = $_GET['id'] ?? '';
    $message = "✅ Laporan disiplin berjaya ditambah untuk " . htmlspecialchars($student_name) .
               " (ID: #" . htmlspecialchars($incident_id) . "). Pelajar dan ibu bapa akan dapat melihat laporan ini secara automatik.";
    $success = true;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_incident'])) {
    // Get form data
    $student_id = (int)$_POST['student_id'];
    $incident_date = trim($_POST['incident_date']);
    $incident_type = trim($_POST['incident_type']);
    $description = trim($_POST['description']);
    $action_taken = trim($_POST['action_taken']);
    $severity = trim($_POST['severity']);

    // Simple validation
    if ($student_id <= 0) {
        $message = "❌ Sila pilih pelajar.";
    } elseif (empty($incident_date)) {
        $message = "❌ Sila masukkan tarikh kejadian.";
    } elseif (empty($incident_type)) {
        $message = "❌ Sila pilih jenis kesalahan.";
    } elseif (empty($description)) {
        $message = "❌ Sila masukkan penerangan kejadian.";
    } elseif (strlen($description) < 10) {
        $message = "❌ Penerangan kejadian mesti sekurang-kurangnya 10 aksara.";
    } elseif (empty($severity)) {
        $message = "❌ Sila pilih tahap keseriusan.";
    } else {
        // Get student information (accessible to all teachers)
        $verifyStmt = $conn->prepare("
            SELECT s.student_id, s.full_name, c.class_name
            FROM students s
            JOIN classrooms c ON s.classroom_id = c.classroom_id
            WHERE s.student_id = ?
        ");
        $verifyStmt->bind_param("i", $student_id);
        $verifyStmt->execute();
        $verifyResult = $verifyStmt->get_result();

        if ($verifyResult->num_rows === 0) {
            $message = "❌ Pelajar yang dipilih tidak dijumpai dalam sistem.";
        } else {
            $studentInfo = $verifyResult->fetch_assoc();

            // Get available columns
            $columns = $conn->query("SHOW COLUMNS FROM discipline_incidents");
            $available_columns = [];
            while ($col = $columns->fetch_assoc()) {
                $available_columns[] = $col['Field'];
            }

            // Build dynamic insert query based on available columns
            $insert_columns = ['student_id', 'teacher_id', 'incident_date', 'incident_type', 'description'];
            $insert_values = [$student_id, $teacher_id, $incident_date, $incident_type, $description];
            $param_types = 'iisss';

            // Add optional columns if they exist
            if (in_array('action_taken', $available_columns)) {
                $insert_columns[] = 'action_taken';
                $insert_values[] = $action_taken;
                $param_types .= 's';
            }

            if (in_array('severity', $available_columns)) {
                $insert_columns[] = 'severity';
                $insert_values[] = $severity;
                $param_types .= 's';
            }

            if (in_array('status', $available_columns)) {
                $insert_columns[] = 'status';
                $insert_values[] = 'Baru';
                $param_types .= 's';
            }

            // Build the SQL query
            $columns_str = implode(', ', $insert_columns);
            $placeholders = str_repeat('?,', count($insert_columns) - 1) . '?';

            $insertSQL = "INSERT INTO discipline_incidents ($columns_str) VALUES ($placeholders)";
            $insertStmt = $conn->prepare($insertSQL);

            if ($insertStmt) {
                $insertStmt->bind_param($param_types, ...$insert_values);

                if ($insertStmt->execute()) {
                    $incident_id = $conn->insert_id;

                    // Log the discipline report creation with details
                    $details = [
                        'Pelajar' => $studentInfo['full_name'],
                        'Kelas' => $studentInfo['class_name'],
                        'Jenis Kesalahan' => $incident_type,
                        'Tahap Keseriusan' => $severity,
                        'Tarikh Kejadian' => $incident_date,
                        'Tindakan Diambil' => !empty($action_taken) ? $action_taken : 'Tiada'
                    ];

                    logUserActivity($conn, 'CREATE', 'DISCIPLINE', $incident_id, $studentInfo['full_name'],
                        "Laporan disiplin dibuat", $details);

                    // Redirect to prevent resubmission
                    header("Location: " . $_SERVER['PHP_SELF'] . "?success=1&student=" . urlencode($studentInfo['full_name']) . "&id=" . $incident_id);
                    exit;
                } else {
                    $message = "❌ Ralat menyimpan ke database: " . $insertStmt->error;
                }
                $insertStmt->close();
            } else {
                $message = "❌ Ralat menyediakan query: " . $conn->error;
            }
        }
        $verifyStmt->close();
    }
}

// Get all students (accessible to all teachers)
$students_array = [];
$students_count = 0;
$debug_info = "";

try {
    $studentsStmt = $conn->prepare("
        SELECT s.student_id, s.full_name, s.no_ic, c.class_name
        FROM students s
        JOIN classrooms c ON s.classroom_id = c.classroom_id
        ORDER BY c.class_name, s.full_name
    ");

    if ($studentsStmt) {
        $studentsStmt->execute();
        $studentsResult = $studentsStmt->get_result();

        while ($student = $studentsResult->fetch_assoc()) {
            $students_array[] = $student;
        }

        $students_count = count($students_array);
        $studentsStmt->close();

        if ($students_count === 0) {
            $debug_info = "Tiada pelajar dijumpai dalam sistem. Sila hubungi pentadbir.";
        }
    } else {
        $debug_info = "Ralat query pelajar: " . $conn->error;
    }
} catch (Exception $e) {
    $debug_info = "Ralat sistem: " . $e->getMessage();
}

// Get teacher's incidents
$incidents = null;
try {
    $incidentsStmt = $conn->prepare("
        SELECT
            di.incident_id,
            di.incident_date,
            di.incident_type,
            di.severity,
            di.status,
            di.created_at,
            s.full_name as student_name,
            s.no_ic as student_ic,
            c.class_name
        FROM discipline_incidents di
        JOIN students s ON di.student_id = s.student_id
        LEFT JOIN classrooms c ON s.classroom_id = c.classroom_id
        WHERE di.teacher_id = ?
        ORDER BY di.created_at DESC
        LIMIT 50
    ");

    if ($incidentsStmt) {
        $incidentsStmt->bind_param("i", $teacher_id);
        $incidentsStmt->execute();
        $incidents = $incidentsStmt->get_result();
        $incidentsStmt->close();
    }
} catch (Exception $e) {
    // Table might not exist yet
    $incidents = null;
}
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laporan Disiplin - SMKTMI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>

<style>
/* Content Styling */
.content {
    margin-left: 280px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.form-container {
    width: 100%;
    max-width: 1200px;
    background-color: #fff;
    padding: 30px 40px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0 auto;
}

.form-container h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 25px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    color: #2980b9;
    text-align: center;
}

.form-section {
    background-color: #f8f9fa;
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 30px;
    border-left: 4px solid #3498db;
}

.incident-table th {
    background-color: rgb(153, 156, 158);
    color: white;
    text-align: center;
    font-weight: 600;
    padding: 12px;
    border: none;
}

.incident-table td {
    padding: 12px;
    text-align: center;
    vertical-align: middle;
    border-bottom: 1px solid #e9ecef;
}

.severity-ringan { background-color: #d4edda; color: #155724; }
.severity-sederhana { background-color: #fff3cd; color: #856404; }
.severity-berat { background-color: #f8d7da; color: #721c24; }

.status-baru { background-color: #cce5ff; color: #004085; }
.status-dalam-tindakan { background-color: #fff3cd; color: #856404; }
.status-selesai { background-color: #d4edda; color: #155724; }

.badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.btn-primary {
    background-color: #3498db;
    border-color: #3498db;
    padding: 10px 30px;
    font-weight: 600;
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

/* Responsive */
@media (max-width: 768px) {
    .content {
        margin-left: 0;
        padding: 20px;
    }
    
    .form-container {
        padding: 20px;
    }
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
  <div class="form-container">
    <h2><i class="fas fa-clipboard-check"></i> Laporan Disiplin</h2>

    <!-- Messages -->
    <?php if (!empty($message)): ?>
      <div class="alert <?php echo $success ? 'alert-success' : 'alert-danger'; ?> alert-dismissible fade show">
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    <?php endif; ?>

    <!-- Debug Information -->
    <?php if (!empty($debug_info)): ?>
    <div class="alert alert-warning">
      <h6><i class="fas fa-info-circle"></i> Maklumat:</h6>
      <p class="mb-0"><?php echo htmlspecialchars($debug_info); ?></p>
    </div>
    <?php endif; ?>

    <!-- Add New Incident Form -->
    <?php if ($students_count > 0): ?>
    <div class="form-section">
      <h5 style="margin-bottom: 20px; color: #2c3e50;">
        <i class="fas fa-plus-circle"></i> Tambah Laporan Disiplin Baru
      </h5>

      <div class="alert alert-info mb-3">
        <i class="fas fa-info-circle"></i> <strong>Nota:</strong> Laporan yang anda buat akan secara automatik kelihatan kepada pelajar dan ibu bapa mereka.
      </div>

      <form method="post" action="">
        <div class="row g-3">
          <div class="col-md-6">
            <label for="student_id" class="form-label"><strong>Pelajar:</strong></label>
            <select name="student_id" id="student_id" class="form-select" required>
              <option value="">-- Pilih Pelajar --</option>
              <?php foreach ($students_array as $student): ?>
                <option value="<?php echo $student['student_id']; ?>">
                  <?php echo htmlspecialchars($student['full_name']); ?>
                  (<?php echo htmlspecialchars($student['class_name']); ?>)
                </option>
              <?php endforeach; ?>
            </select>
          </div>
          <div class="col-md-6">
            <label for="incident_date" class="form-label"><strong>Tarikh Kejadian:</strong></label>
            <input type="date" name="incident_date" id="incident_date" class="form-control"
                   value="<?php echo date('Y-m-d'); ?>" required>
          </div>

          <div class="col-md-6">
            <label for="incident_type" class="form-label"><strong>Jenis Kesalahan:</strong></label>
            <select name="incident_type" id="incident_type" class="form-select" required>
              <option value="">-- Pilih Jenis --</option>
              <option value="Ponteng Kelas">Ponteng Kelas</option>
              <option value="Tidak Memakai Pakaian Seragam Lengkap">Tidak Memakai Pakaian Seragam Lengkap</option>
              <option value="Berkelakuan Tidak Senonoh">Berkelakuan Tidak Senonoh</option>
              <option value="Meniru Semasa Peperiksaan">Meniru Semasa Peperiksaan</option>
              <option value="Membawa Telefon Bimbit Tanpa Kebenaran">Membawa Telefon Bimbit Tanpa Kebenaran</option>
              <option value="Merokok di Kawasan Sekolah">Merokok di Kawasan Sekolah</option>
              <option value="Bergaduh">Bergaduh</option>
              <option value="Vandalisme">Vandalisme</option>
              <option value="Merokok">Merokok</option>
              <option value="Buli">Buli</option>
            </select>
          </div>

          <div class="col-md-6">
            <label for="severity" class="form-label"><strong>Tahap Keseriusan:</strong></label>
            <select name="severity" id="severity" class="form-select" required>
              <option value="Ringan">Ringan</option>
              <option value="Sederhana">Sederhana</option>
              <option value="Berat">Berat</option>
            </select>
          </div>
          <div class="col-12">
            <label for="description" class="form-label"><strong>Penerangan Kejadian:</strong></label>
            <textarea name="description" id="description" class="form-control" rows="4"
                      placeholder="Terangkan kejadian secara terperinci..." required></textarea>
            <small class="text-muted">Minimum 10 aksara</small>
          </div>

          <div class="col-12">
            <label for="action_taken" class="form-label"><strong>Tindakan Diambil:</strong></label>
            <textarea name="action_taken" id="action_taken" class="form-control" rows="3"
                      placeholder="Nyatakan tindakan yang telah diambil (jika ada)..."></textarea>
          </div>

          <div class="col-12">
            <button type="submit" name="add_incident" class="btn btn-primary">
              <i class="fas fa-save"></i> Simpan Laporan
            </button>
            <button type="reset" class="btn btn-secondary ms-2">
              <i class="fas fa-undo"></i> Reset Form
            </button>
          </div>
        </div>
      </form>
    </div>
    <?php else: ?>
    <div class="alert alert-warning">
      <h5><i class="fas fa-exclamation-triangle"></i> Tiada Pelajar Dijumpai</h5>
      <p><strong>Kemungkinan sebab:</strong></p>
      <ul class="mb-2">
        <li>Belum ada pelajar yang didaftarkan dalam sistem</li>
        <li>Terdapat masalah dengan pangkalan data pelajar</li>
      </ul>
      <p><strong>Tindakan:</strong> Sila hubungi pentadbir sistem untuk menyemak pendaftaran pelajar.</p>
      <?php if (!empty($debug_info)): ?>
      <hr>
      <small class="text-muted">Maklumat: <?php echo htmlspecialchars($debug_info); ?></small>
      <?php endif; ?>
    </div>
    <?php endif; ?>

    <!-- Incidents History -->
    <div class="mt-5">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <h5 style="color: #2c3e50; margin: 0;">
          <i class="fas fa-history"></i> Sejarah Laporan Disiplin
        </h5>
        <?php if ($incidents && $incidents->num_rows > 0): ?>
        <small class="text-muted">
          Menunjukkan <?php echo $incidents->num_rows; ?> laporan terkini
        </small>
        <?php endif; ?>
      </div>

      <div class="table-responsive">
        <table class="table table-bordered incident-table">
          <thead>
            <tr>
              <th style="width: 50px;">Bil.</th>
              <th style="width: 100px;">Tarikh</th>
              <th style="width: 200px;">Pelajar</th>
              <th style="width: 100px;">Kelas</th>
              <th style="width: 180px;">Jenis Kesalahan</th>
              <th style="width: 100px;">Tahap</th>
              <th style="width: 100px;">Status</th>
              <th style="width: 120px;">Tindakan</th>
            </tr>
          </thead>
          <tbody>
            <?php if ($incidents && $incidents->num_rows > 0): ?>
              <?php $bil = 1; while ($incident = $incidents->fetch_assoc()): ?>
                <tr>
                  <td class="text-center"><?php echo $bil++; ?></td>
                  <td class="text-center">
                    <?php echo date('d/m/Y', strtotime($incident['incident_date'])); ?>
                  </td>
                  <td>
                    <strong><?php echo htmlspecialchars($incident['student_name']); ?></strong>
                    <?php if (!empty($incident['student_ic'])): ?>
                    <br><small class="text-muted"><?php echo htmlspecialchars($incident['student_ic']); ?></small>
                    <?php endif; ?>
                  </td>
                  <td class="text-center">
                    <?php echo htmlspecialchars($incident['class_name'] ?? '-'); ?>
                  </td>
                  <td>
                    <span class="fw-bold"><?php echo htmlspecialchars($incident['incident_type']); ?></span>
                  </td>
                  <td class="text-center">
                    <span class="badge severity-<?php echo strtolower($incident['severity']); ?>">
                      <?php echo $incident['severity']; ?>
                    </span>
                  </td>
                  <td class="text-center">
                    <span class="badge status-<?php echo strtolower(str_replace(' ', '-', $incident['status'])); ?>">
                      <?php echo $incident['status']; ?>
                    </span>
                  </td>
                  <td class="text-center">
                    <div class="btn-group" role="group">
                      <button class="btn btn-sm btn-info" onclick="viewDetails(<?php echo $incident['incident_id']; ?>)"
                              title="Lihat Butiran">
                        <i class="fas fa-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-warning" onclick="editIncident(<?php echo $incident['incident_id']; ?>)"
                              title="Edit Laporan">
                        <i class="fas fa-edit"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              <?php endwhile; ?>
            <?php else: ?>
              <tr>
                <td colspan="8" class="text-center py-4">
                  <i class="fas fa-info-circle text-muted"></i>
                  <p class="mb-0 text-muted">Tiada laporan disiplin dijumpai.</p>
                  <small class="text-muted">Laporan yang anda buat akan dipaparkan di sini.</small>
                  <?php if (isset($_GET['debug'])): ?>
                  <br><small class="text-danger">
                    Debug: Incidents query returned <?php echo $incidents ? $incidents->num_rows : 'NULL'; ?> rows
                  </small>
                  <?php endif; ?>
                </td>
              </tr>
            <?php endif; ?>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<!-- View Details Modal -->
<div class="modal fade" id="viewModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Butiran Laporan Disiplin</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body" id="viewModalBody">
        <div class="text-center">
          <i class="fas fa-spinner fa-spin fa-2x"></i>
          <p>Memuatkan butiran...</p>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
      </div>
    </div>
  </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Edit Laporan Disiplin</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body" id="editModalBody">
        <div class="text-center">
          <i class="fas fa-spinner fa-spin fa-2x"></i>
          <p>Memuatkan data...</p>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
        <button type="button" class="btn btn-primary" id="saveEditBtn">Simpan Perubahan</button>
      </div>
    </div>
  </div>
</div>

<script>
// View incident details
function viewDetails(incidentId) {
    const modal = new bootstrap.Modal(document.getElementById('viewModal'));
    modal.show();

    // Load incident details via AJAX
    fetch('get_incident_details.php?id=' + incidentId)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const incident = data.incident;
                const modalBody = document.getElementById('viewModalBody');

                modalBody.innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">Maklumat Pelajar</h6>
                            <table class="table table-sm">
                                <tr><td><strong>Nama:</strong></td><td>${incident.student_name}</td></tr>
                                <tr><td><strong>No. IC:</strong></td><td>${incident.student_ic || '-'}</td></tr>
                                <tr><td><strong>Kelas:</strong></td><td>${incident.class_name}</td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary">Maklumat Kejadian</h6>
                            <table class="table table-sm">
                                <tr><td><strong>Tarikh:</strong></td><td>${incident.incident_date_display || incident.incident_date}</td></tr>
                                <tr><td><strong>Jenis:</strong></td><td>${incident.incident_type}</td></tr>
                                <tr><td><strong>Tahap:</strong></td><td><span class="badge bg-${getSeverityColor(incident.severity)}">${incident.severity}</span></td></tr>
                                <tr><td><strong>Status:</strong></td><td><span class="badge bg-${getStatusColor(incident.status)}">${incident.status}</span></td></tr>
                            </table>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6 class="text-primary">Penerangan Kejadian</h6>
                            <div class="border p-3 bg-light rounded">
                                ${incident.description}
                            </div>
                        </div>
                    </div>
                    ${incident.action_taken ? `
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6 class="text-primary">Tindakan Diambil</h6>
                            <div class="border p-3 bg-light rounded">
                                ${incident.action_taken}
                            </div>
                        </div>
                    </div>
                    ` : ''}
                    <div class="row mt-3">
                        <div class="col-12">
                            <small class="text-muted">
                                <i class="fas fa-clock"></i> Dibuat pada: ${incident.created_at}
                                ${incident.updated_at !== incident.created_at ? `<br><i class="fas fa-edit"></i> Dikemaskini: ${incident.updated_at}` : ''}
                            </small>
                        </div>
                    </div>
                `;
            } else {
                document.getElementById('viewModalBody').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i> ${data.message || 'Ralat memuatkan data'}
                    </div>
                `;
            }
        })
        .catch(error => {
            document.getElementById('viewModalBody').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> Ralat sambungan: ${error.message}
                </div>
            `;
        });
}

// Edit incident
function editIncident(incidentId) {
    const modal = new bootstrap.Modal(document.getElementById('editModal'));
    modal.show();

    // Load incident data for editing
    fetch('get_incident_details.php?id=' + incidentId)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const incident = data.incident;
                const modalBody = document.getElementById('editModalBody');

                modalBody.innerHTML = `
                    <form id="editForm">
                        <input type="hidden" id="edit_incident_id" value="${incident.incident_id}">

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label"><strong>Pelajar:</strong></label>
                                <input type="text" class="form-control" value="${incident.student_name} (${incident.class_name})" readonly>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label"><strong>Tarikh Kejadian:</strong></label>
                                <input type="date" id="edit_incident_date" class="form-control" value="${incident.incident_date}">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label"><strong>Jenis Kesalahan:</strong></label>
                                <select id="edit_incident_type" class="form-select">
                                    <option value="Ponteng Kelas" ${incident.incident_type === 'Ponteng Kelas' ? 'selected' : ''}>Ponteng Kelas</option>
                                    <option value="Tidak Memakai Pakaian Seragam Lengkap" ${incident.incident_type === 'Tidak Memakai Pakaian Seragam Lengkap' ? 'selected' : ''}>Tidak Memakai Pakaian Seragam Lengkap</option>
                                    <option value="Berkelakuan Tidak Senonoh" ${incident.incident_type === 'Berkelakuan Tidak Senonoh' ? 'selected' : ''}>Berkelakuan Tidak Senonoh</option>
                                    <option value="Meniru Semasa Peperiksaan" ${incident.incident_type === 'Meniru Semasa Peperiksaan' ? 'selected' : ''}>Meniru Semasa Peperiksaan</option>
                                    <option value="Membawa Telefon Bimbit Tanpa Kebenaran" ${incident.incident_type === 'Membawa Telefon Bimbit Tanpa Kebenaran' ? 'selected' : ''}>Membawa Telefon Bimbit Tanpa Kebenaran</option>
                                    <option value="Merokok di Kawasan Sekolah" ${incident.incident_type === 'Merokok di Kawasan Sekolah' ? 'selected' : ''}>Merokok di Kawasan Sekolah</option>
                                    <option value="Bergaduh" ${incident.incident_type === 'Bergaduh' ? 'selected' : ''}>Bergaduh</option>
                                    <option value="Vandalisme" ${incident.incident_type === 'Vandalisme' ? 'selected' : ''}>Vandalisme</option>
                                    <option value="Merokok" ${incident.incident_type === 'Merokok' ? 'selected' : ''}>Merokok</option>
                                    <option value="Buli" ${incident.incident_type === 'Buli' ? 'selected' : ''}>Buli</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label"><strong>Tahap Keseriusan:</strong></label>
                                <select id="edit_severity" class="form-select">
                                    <option value="Ringan" ${incident.severity === 'Ringan' ? 'selected' : ''}>Ringan</option>
                                    <option value="Sederhana" ${incident.severity === 'Sederhana' ? 'selected' : ''}>Sederhana</option>
                                    <option value="Berat" ${incident.severity === 'Berat' ? 'selected' : ''}>Berat</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label"><strong>Status:</strong></label>
                            <select id="edit_status" class="form-select">
                                <option value="Baru" ${incident.status === 'Baru' ? 'selected' : ''}>Baru</option>
                                <option value="Dalam Tindakan" ${incident.status === 'Dalam Tindakan' ? 'selected' : ''}>Dalam Tindakan</option>
                                <option value="Selesai" ${incident.status === 'Selesai' ? 'selected' : ''}>Selesai</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label"><strong>Penerangan Kejadian:</strong></label>
                            <textarea id="edit_description" class="form-control" rows="4">${incident.description}</textarea>
                        </div>

                        <div class="mb-3">
                            <label class="form-label"><strong>Tindakan Diambil:</strong></label>
                            <textarea id="edit_action_taken" class="form-control" rows="3">${incident.action_taken || ''}</textarea>
                        </div>
                    </form>
                `;

                // Set up save button
                document.getElementById('saveEditBtn').onclick = function() {
                    saveIncidentChanges();
                };

            } else {
                document.getElementById('editModalBody').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i> ${data.message || 'Ralat memuatkan data'}
                    </div>
                `;
            }
        })
        .catch(error => {
            document.getElementById('editModalBody').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> Ralat sambungan: ${error.message}
                </div>
            `;
        });
}

// Save incident changes
function saveIncidentChanges() {
    const formData = new FormData();
    formData.append('incident_id', document.getElementById('edit_incident_id').value);
    formData.append('incident_date', document.getElementById('edit_incident_date').value);
    formData.append('incident_type', document.getElementById('edit_incident_type').value);
    formData.append('severity', document.getElementById('edit_severity').value);
    formData.append('status', document.getElementById('edit_status').value);
    formData.append('description', document.getElementById('edit_description').value);
    formData.append('action_taken', document.getElementById('edit_action_taken').value);

    fetch('update_incident.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ Laporan berjaya dikemaskini!');
            bootstrap.Modal.getInstance(document.getElementById('editModal')).hide();
            location.reload(); // Refresh page to show updated data
        } else {
            alert('❌ Ralat: ' + (data.message || 'Gagal mengemaskini laporan'));
        }
    })
    .catch(error => {
        alert('❌ Ralat sambungan: ' + error.message);
    });
}

// Helper functions for badge colors
function getSeverityColor(severity) {
    switch(severity) {
        case 'Ringan': return 'success';
        case 'Sederhana': return 'warning';
        case 'Berat': return 'danger';
        default: return 'secondary';
    }
}

function getStatusColor(status) {
    switch(status) {
        case 'Baru': return 'primary';
        case 'Dalam Tindakan': return 'warning';
        case 'Selesai': return 'success';
        default: return 'secondary';
    }
}

// Simple form validation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const studentId = document.getElementById('student_id').value;
            const incidentDate = document.getElementById('incident_date').value;
            const incidentType = document.getElementById('incident_type').value;
            const description = document.getElementById('description').value;
            const severity = document.getElementById('severity').value;

            if (!studentId) {
                alert('Sila pilih pelajar');
                e.preventDefault();
                return false;
            }

            if (!incidentDate) {
                alert('Sila masukkan tarikh kejadian');
                e.preventDefault();
                return false;
            }

            if (!incidentType) {
                alert('Sila pilih jenis kesalahan');
                e.preventDefault();
                return false;
            }

            if (!description || description.length < 10) {
                alert('Penerangan kejadian mesti sekurang-kurangnya 10 aksara');
                e.preventDefault();
                return false;
            }

            if (!severity) {
                alert('Sila pilih tahap keseriusan');
                e.preventDefault();
                return false;
            }

            return true;
        });
    }
});
</script>

</body>
</html>
