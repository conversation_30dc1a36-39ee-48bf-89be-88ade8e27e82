<?php
require 'db.php';

if (!isset($_GET['token'])) {
    die("Token tidak sah.");
}

$token = $_GET['token'];
$current_time = new DateTime();
$token_valid_duration = new DateInterval('PT24H'); // 24 jam

$stmt = $conn->prepare("SELECT parent_id, token_created_at FROM parents WHERE verification_token = ?");
if (!$stmt) {
    die("Prepare failed: (" . $conn->errno . ") " . $conn->error);
}
$stmt->bind_param("s", $token);
$stmt->execute();
$result = $stmt->get_result();

if ($row = $result->fetch_assoc()) {
    $token_created_at = new DateTime($row['token_created_at']);
    $token_expiry = clone $token_created_at;
    $token_expiry->add($token_valid_duration);

    if ($token_expiry < $current_time) {
        die("Token telah tamat tempoh.");
    }

    $updateStmt = $conn->prepare("UPDATE parents SET email_verified = 1, verification_token = NULL, token_created_at = NULL WHERE parent_id = ?");
    if (!$updateStmt) {
        die("Prepare failed (update): (" . $conn->errno . ") " . $conn->error);
    }
    $updateStmt->bind_param("i", $row['parent_id']);
    $updateStmt->execute();

    header("Location: login.php?verified=1");
    exit();
} else {
    die("Token tidak sah.");
}
?>
