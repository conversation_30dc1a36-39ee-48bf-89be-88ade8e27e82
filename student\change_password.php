<?php
session_start();

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Student') {
    header("Location: ../login.php");
    exit;
}

require '../db.php';
require '../includes/universal_logger.php';

$userId = $_SESSION['user_id'];
$message = '';
$messageType = '';

// Handle password change form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $current_password = $_POST['current_password'] ?? '';
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    // Validation
    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
        $message = "Semua medan diperlukan.";
        $messageType = 'danger';
    } elseif ($new_password !== $confirm_password) {
        $message = "Kata laluan baru dan pengesahan tidak sepadan.";
        $messageType = 'danger';
    } elseif (strlen($new_password) < 8) {
        $message = "Kata laluan baru mestilah sekurang-kurangnya 8 aksara.";
        $messageType = 'danger';
    } elseif (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/', $new_password)) {
        $message = "Kata laluan baru mesti mengandungi huruf besar, huruf kecil, nombor dan simbol.";
        $messageType = 'danger';
    } else {
        // Verify current password
        $stmt = $conn->prepare("SELECT password_hash FROM users WHERE user_id = ?");
        $stmt->bind_param("i", $userId);
        $stmt->execute();
        $result = $stmt->get_result();
        $user = $result->fetch_assoc();
        $stmt->close();
        
        if ($user && password_verify($current_password, $user['password_hash'])) {
            // Update password
            $new_password_hash = password_hash($new_password, PASSWORD_DEFAULT);
            $updateStmt = $conn->prepare("UPDATE users SET password_hash = ? WHERE user_id = ?");
            $updateStmt->bind_param("si", $new_password_hash, $userId);
            
            if ($updateStmt->execute()) {
                // Log the password change with details
                $details = [
                    'Jenis Perubahan' => 'Kata Laluan',
                    'Status' => 'Berjaya',
                    'Keselamatan' => 'Memenuhi keperluan keselamatan'
                ];

                logUserActivity($conn, 'UPDATE', 'PROFILE', $userId, $_SESSION['name'],
                    "Kata laluan dikemaskini", $details);

                $message = "Kata laluan berjaya dikemaskini.";
                $messageType = 'success';
            } else {
                $message = "Ralat semasa mengemaskini kata laluan. Sila cuba lagi.";
                $messageType = 'danger';
            }
            $updateStmt->close();
        } else {
            $message = "Kata laluan semasa tidak betul.";
            $messageType = 'danger';
        }
    }
}

// Get student details for display
$stmt = $conn->prepare("SELECT s.full_name, s.no_ic, c.class_name, u.email FROM students s LEFT JOIN classrooms c ON s.classroom_id = c.classroom_id JOIN users u ON s.user_id = u.user_id WHERE s.user_id = ?");
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();
$student = $result->fetch_assoc();
$stmt->close();
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tukar Kata Laluan - SMKTMI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>

<style>
/* Content Styling */
.content {
    margin-left: 300px;
    padding: 40px 30px;
    min-height: 100vh;
    background-color: #f9f9f9;
}

.form-container {
    width: 100%;
    max-width: 600px;
    background-color: #fff;
    padding: 40px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0 auto;
}

.form-container h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 30px;
    border-bottom: 3px solid #9b59b6;
    padding-bottom: 15px;
    color: #8e44ad;
    text-align: center;
}

.form-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
}

.form-control {
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    padding: 12px 15px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: #9b59b6;
    box-shadow: 0 0 0 0.2rem rgba(155, 89, 182, 0.25);
}

.btn-primary {
    background-color: #9b59b6;
    border-color: #9b59b6;
    padding: 12px 30px;
    font-weight: 600;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #8e44ad;
    border-color: #8e44ad;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: #95a5a6;
    border-color: #95a5a6;
    padding: 12px 30px;
    font-weight: 600;
    border-radius: 6px;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
    border-color: #7f8c8d;
}

.alert {
    border-radius: 6px;
    padding: 15px 20px;
    margin-bottom: 25px;
    border: none;
}

.password-requirements {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
}

.student-info {
    background-color: #f4f0ff;
    border: 1px solid #d1c4e9;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 25px;
    text-align: center;
}

.student-info h5 {
    color: #673ab7;
    margin-bottom: 5px;
}

.student-info p {
    color: #424242;
    margin: 0;
}

.input-group .btn-outline-secondary {
    border-color: #e0e0e0;
    color: #6c757d;
    transition: all 0.3s ease;
}

.input-group .btn-outline-secondary:hover {
    background-color: #9b59b6;
    border-color: #9b59b6;
    color: white;
}

.input-group .btn-outline-secondary:focus {
    box-shadow: 0 0 0 0.2rem rgba(155, 89, 182, 0.25);
}

/* Responsive */
@media (max-width: 768px) {
    .content {
        margin-left: 0;
        padding: 20px;
    }
    
    .form-container {
        padding: 30px 20px;
    }
}
</style>

<?php include 'includes/sidebar.php'; ?>

<div class="content">
  <div class="form-container">
    <h2><i class="fas fa-key"></i> Tukar Kata Laluan</h2>

    <!-- Student Info -->
    <div class="student-info">
      <h5><i class="fas fa-user-graduate"></i> <?php echo htmlspecialchars($student['full_name']); ?></h5>
      <p><strong>No. IC:</strong> <?php echo htmlspecialchars($student['no_ic']); ?></p>
      <p><strong>Kelas:</strong> <?php echo htmlspecialchars($student['class_name'] ?? 'Belum Ditetapkan'); ?></p>
      <p><strong>Email:</strong> <?php echo htmlspecialchars($student['email']); ?></p>
    </div>

    <?php if (!empty($message)): ?>
      <div class="alert alert-<?php echo $messageType; ?>">
        <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
        <?php echo htmlspecialchars($message); ?>
      </div>
    <?php endif; ?>

    <!-- Password Requirements -->
    <div class="password-requirements">
      <h6><i class="fas fa-shield-alt"></i> Keperluan Kata Laluan</h6>
      <ul>
        <li><strong>Minima 8 aksara</strong></li>
        <li><strong>Mesti ada huruf besar</strong> (A-Z)</li>
        <li><strong>Mesti ada huruf kecil</strong> (a-z)</li>
        <li><strong>Mesti ada nombor</strong> (0-9)</li>
        <li><strong>Mesti ada simbol</strong> (@$!%*?&)</li>
        <li>Elakkan menggunakan nama atau tarikh lahir</li>
        <li>Jangan kongsi kata laluan dengan rakan</li>
        <li>Kata laluan yang kuat melindungi maklumat akademik anda</li>
      </ul>
    </div>

    <form method="post" id="passwordForm">
      <div class="mb-3">
        <label for="current_password" class="form-label">
          <i class="fas fa-lock"></i> Kata Laluan Semasa
        </label>
        <input type="password" class="form-control" id="current_password" name="current_password" required>
      </div>

      <div class="mb-3">
        <label for="new_password" class="form-label">
          <i class="fas fa-key"></i> Kata Laluan Baru
        </label>
        <div class="input-group">
          <input type="password" class="form-control" id="new_password" name="new_password" required minlength="8">
          <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('new_password', 'toggleNewPassword')">
            <i class="fas fa-eye" id="toggleNewPassword"></i>
          </button>
        </div>
      </div>

      <div class="mb-4">
        <label for="confirm_password" class="form-label">
          <i class="fas fa-check-double"></i> Sahkan Kata Laluan Baru
        </label>
        <div class="input-group">
          <input type="password" class="form-control" id="confirm_password" name="confirm_password" required minlength="8">
          <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password', 'toggleConfirmPassword')">
            <i class="fas fa-eye" id="toggleConfirmPassword"></i>
          </button>
        </div>
      </div>

      <div class="d-grid gap-2 d-md-flex justify-content-md-center">
        <button type="submit" class="btn btn-primary me-md-2">
          <i class="fas fa-save"></i> Kemaskini Kata Laluan
        </button>
        <a href="dashboard.php" class="btn btn-secondary">
          <i class="fas fa-arrow-left"></i> Kembali ke Dashboard
        </a>
      </div>
    </form>
  </div>
</div>

<script>
// Password confirmation validation
document.getElementById('passwordForm').addEventListener('submit', function(e) {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = document.getElementById('confirm_password').value;

    if (newPassword !== confirmPassword) {
        e.preventDefault();
        alert('Kata laluan baru dan pengesahan tidak sepadan.');
        return false;
    }

    if (newPassword.length < 8) {
        e.preventDefault();
        alert('Kata laluan baru mestilah sekurang-kurangnya 8 aksara.');
        return false;
    }

    // Check password complexity
    const hasUpperCase = /[A-Z]/.test(newPassword);
    const hasLowerCase = /[a-z]/.test(newPassword);
    const hasNumbers = /\d/.test(newPassword);
    const hasSymbols = /[@$!%*?&]/.test(newPassword);

    if (!hasUpperCase || !hasLowerCase || !hasNumbers || !hasSymbols) {
        e.preventDefault();
        alert('Kata laluan baru mesti mengandungi huruf besar, huruf kecil, nombor dan simbol (@$!%*?&).');
        return false;
    }
});

// Show/hide password toggle
function togglePassword(inputId, iconId) {
    const input = document.getElementById(inputId);
    const icon = document.getElementById(iconId);

    if (input.getAttribute('type') === 'password') {
        input.setAttribute('type', 'text');
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.setAttribute('type', 'password');
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}
</script>

</body>
</html>
